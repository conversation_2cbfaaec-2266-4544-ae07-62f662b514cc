// 默认AI模型服务 - 管理不同使用场景的默认AI模型配置

import { ChromeStorageService } from '../utils/chromeStorage'
import { aiIntegrationService } from './aiIntegrationService'
import { AIModel, AIProviderConfig } from '../types/ai'

/**
 * 默认AI模型使用场景配置
 */
export interface DefaultAIModelUsage {
  id: string
  name: string
  description: string
  category: 'chat' | 'translation' | 'analysis' | 'generation'
  selectedModelId: string | null
  fallbackModelId: string | null
  enabled: boolean
  priority: number
}

/**
 * 可用的AI模型信息
 */
export interface AvailableAIModel {
  id: string
  name: string
  displayName: string
  provider: string
  providerId: string
  modelType: 'chat' | 'embedding' | 'completion'
  enabled: boolean
  status: 'connected' | 'disconnected' | 'error'
  isRecommended?: boolean
  isPopular?: boolean
  selectedAt?: string // 添加选择时间字段
}

/**
 * 默认AI模型服务类
 */
export class DefaultAIModelService {

  /**
   * 存储键名
   */
  private static readonly STORAGE_KEY = 'default_ai_models'

  /**
   * 事件监听器是否已初始化
   */
  private static eventListenerInitialized = false
  
  /**
   * 默认使用场景配置
   */
  private static readonly DEFAULT_USAGES: Omit<DefaultAIModelUsage, 'selectedModelId' | 'fallbackModelId'>[] = [
    {
      id: 'default-chat',
      name: '默认聊天',
      description: '通用对话和问答场景',
      category: 'chat',
      enabled: true,
      priority: 1
    },
    {
      id: 'translation',
      name: '翻译模型',
      description: '文本翻译和多语言处理',
      category: 'translation',
      enabled: true,
      priority: 2
    },
    {
      id: 'tag-naming',
      name: '标签命名',
      description: '为收藏内容生成合适的标签名称',
      category: 'generation',
      enabled: true,
      priority: 3
    },
    {
      id: 'folder-naming',
      name: '文件夹命名',
      description: '为收藏分类生成合适的文件夹名称',
      category: 'generation',
      enabled: true,
      priority: 4
    },
    {
      id: 'content-analysis',
      name: '内容分析',
      description: '分析网页内容并提取关键信息',
      category: 'analysis',
      enabled: true,
      priority: 5
    },
    {
      id: 'summary-generation',
      name: '摘要生成',
      description: '为收藏内容生成简洁摘要',
      category: 'generation',
      enabled: true,
      priority: 6
    }
  ]

  /**
   * 获取所有可用的AI模型（只显示在AI集成页面中已选择的模型）
   * @returns Promise<AvailableAIModel[]>
   */
  async getAvailableModels(): Promise<AvailableAIModel[]> {
    try {
      // 初始化事件监听器
      this.initializeEventListeners()

      const providers = await aiIntegrationService.getConfiguredProviders()
      const selectedModels = await aiIntegrationService.getSelectedModels()
      const availableModels: AvailableAIModel[] = []

      for (const provider of providers) {
        if (!provider.enabled) continue

        // 检查该提供商是否有选中的模型
        const selectedModel = selectedModels[provider.id]
        if (!selectedModel) {
          console.log(`提供商 ${provider.name} 没有选中的模型，跳过`)
          continue
        }

        try {
          // 测试连接状态
          const connectionResult = await aiIntegrationService.testConnection(provider.id)
          const status = connectionResult.success ? 'connected' : 'error'

          if (status === 'connected') {
            // 获取该提供商的模型列表
            const models = await aiIntegrationService.getAvailableModels(provider.id)

            // 只添加用户在AI集成页面中选择的模型
            const chosenModel = models.find(model => model.id === selectedModel.modelId)
            if (chosenModel) {
              availableModels.push({
                id: `${provider.id}_${chosenModel.id}`,
                name: chosenModel.name,
                displayName: chosenModel.displayName,
                provider: provider.name,
                providerId: provider.id,
                modelType: 'chat', // 目前主要支持聊天模型
                enabled: true,
                status: 'connected',
                isRecommended: chosenModel.isRecommended,
                isPopular: chosenModel.isPopular,
                selectedAt: selectedModel.selectedAt // 添加选择时间信息
              })
              console.log(`添加已选择的模型: ${provider.name} - ${chosenModel.displayName}`)
            } else {
              console.warn(`提供商 ${provider.name} 的选中模型 ${selectedModel.modelId} 不在可用模型列表中`)
            }
          } else {
            console.warn(`提供商 ${provider.name} 连接失败，状态: ${status}`)
          }
        } catch (error) {
          console.warn(`获取提供商 ${provider.name} 的模型失败:`, error)
        }
      }

      console.log(`获取到 ${availableModels.length} 个已选择的可用模型`)
      return availableModels
    } catch (error) {
      console.error('获取可用AI模型失败:', error)
      return []
    }
  }

  /**
   * 获取默认AI模型配置
   * @returns Promise<DefaultAIModelUsage[]>
   */
  async getDefaultModelUsages(): Promise<DefaultAIModelUsage[]> {
    try {
      const savedUsages = await ChromeStorageService.getSyncSetting(DefaultAIModelService.STORAGE_KEY, [])
      
      // 如果没有保存的配置，使用默认配置
      if (savedUsages.length === 0) {
        const defaultUsages = DefaultAIModelService.DEFAULT_USAGES.map(usage => ({
          ...usage,
          selectedModelId: null,
          fallbackModelId: null
        }))
        
        await this.saveDefaultModelUsages(defaultUsages)
        return defaultUsages
      }

      return savedUsages
    } catch (error) {
      console.error('获取默认AI模型配置失败:', error)
      return DefaultAIModelService.DEFAULT_USAGES.map(usage => ({
        ...usage,
        selectedModelId: null,
        fallbackModelId: null
      }))
    }
  }

  /**
   * 保存默认AI模型配置
   * @param usages 使用场景配置
   * @returns Promise<void>
   */
  async saveDefaultModelUsages(usages: DefaultAIModelUsage[]): Promise<void> {
    try {
      await ChromeStorageService.saveSyncSetting(DefaultAIModelService.STORAGE_KEY, usages)
      console.log('默认AI模型配置保存成功')
    } catch (error) {
      console.error('保存默认AI模型配置失败:', error)
      throw error
    }
  }

  /**
   * 更新单个使用场景的模型配置
   * @param usageId 使用场景ID
   * @param selectedModelId 选中的模型ID
   * @param fallbackModelId 备用模型ID
   * @returns Promise<void>
   */
  async updateUsageModel(usageId: string, selectedModelId: string | null, fallbackModelId: string | null): Promise<void> {
    try {
      const usages = await this.getDefaultModelUsages()
      const updatedUsages = usages.map(usage => 
        usage.id === usageId 
          ? { ...usage, selectedModelId, fallbackModelId }
          : usage
      )
      
      await this.saveDefaultModelUsages(updatedUsages)
      console.log(`使用场景 ${usageId} 的模型配置已更新`)
    } catch (error) {
      console.error('更新使用场景模型配置失败:', error)
      throw error
    }
  }

  /**
   * 获取指定使用场景的推荐模型
   * @param usageId 使用场景ID
   * @returns Promise<AvailableAIModel | null>
   */
  async getRecommendedModel(usageId: string): Promise<AvailableAIModel | null> {
    try {
      const usages = await this.getDefaultModelUsages()
      const usage = usages.find(u => u.id === usageId)
      
      if (!usage || !usage.selectedModelId) {
        return null
      }

      const availableModels = await this.getAvailableModels()
      return availableModels.find(model => model.id === usage.selectedModelId) || null
    } catch (error) {
      console.error('获取推荐模型失败:', error)
      return null
    }
  }

  /**
   * 获取指定使用场景的备用模型
   * @param usageId 使用场景ID
   * @returns Promise<AvailableAIModel | null>
   */
  async getFallbackModel(usageId: string): Promise<AvailableAIModel | null> {
    try {
      const usages = await this.getDefaultModelUsages()
      const usage = usages.find(u => u.id === usageId)
      
      if (!usage || !usage.fallbackModelId) {
        return null
      }

      const availableModels = await this.getAvailableModels()
      return availableModels.find(model => model.id === usage.fallbackModelId) || null
    } catch (error) {
      console.error('获取备用模型失败:', error)
      return null
    }
  }

  /**
   * 一键设置推荐配置
   * @returns Promise<void>
   */
  async setRecommendedConfiguration(): Promise<void> {
    try {
      const availableModels = await this.getAvailableModels()

      if (availableModels.length === 0) {
        throw new Error('没有可用的AI模型，请先在AI集成页面配置模型')
      }

      // 按推荐程度和受欢迎程度排序
      const sortedModels = availableModels.sort((a, b) => {
        if (a.isRecommended && !b.isRecommended) return -1
        if (!a.isRecommended && b.isRecommended) return 1
        if (a.isPopular && !b.isPopular) return -1
        if (!a.isPopular && b.isPopular) return 1
        return 0
      })

      const primaryModel = sortedModels[0]
      const fallbackModel = sortedModels.length > 1 ? sortedModels[1] : null

      const usages = await this.getDefaultModelUsages()
      const updatedUsages = usages.map(usage => ({
        ...usage,
        selectedModelId: primaryModel.id,
        fallbackModelId: fallbackModel?.id || null
      }))

      await this.saveDefaultModelUsages(updatedUsages)
      console.log('推荐配置设置成功')
    } catch (error) {
      console.error('设置推荐配置失败:', error)
      throw error
    }
  }

  /**
   * 使用指定的模型设置推荐配置
   * @param primaryModelId 主要模型ID
   * @param fallbackModelId 备选模型ID（可选）
   * @returns Promise<void>
   */
  async setRecommendedConfigurationWithModels(primaryModelId: string, fallbackModelId: string | null = null): Promise<void> {
    try {
      const availableModels = await this.getAvailableModels()

      // 验证主要模型是否存在
      const primaryModel = availableModels.find(model => model.id === primaryModelId)
      if (!primaryModel) {
        throw new Error(`指定的主要模型不存在: ${primaryModelId}`)
      }

      // 验证备选模型是否存在（如果指定了）
      if (fallbackModelId) {
        const fallbackModel = availableModels.find(model => model.id === fallbackModelId)
        if (!fallbackModel) {
          throw new Error(`指定的备选模型不存在: ${fallbackModelId}`)
        }
      }

      const usages = await this.getDefaultModelUsages()
      const updatedUsages = usages.map(usage => ({
        ...usage,
        selectedModelId: primaryModelId,
        fallbackModelId: fallbackModelId
      }))

      await this.saveDefaultModelUsages(updatedUsages)
      console.log(`推荐配置设置成功 - 主要模型: ${primaryModel.displayName}, 备选模型: ${fallbackModelId ? availableModels.find(m => m.id === fallbackModelId)?.displayName || '无' : '无'}`)
    } catch (error) {
      console.error('设置推荐配置失败:', error)
      throw error
    }
  }

  /**
   * 重置为默认配置
   * @returns Promise<void>
   */
  async resetToDefaults(): Promise<void> {
    try {
      const defaultUsages = DefaultAIModelService.DEFAULT_USAGES.map(usage => ({
        ...usage,
        selectedModelId: null,
        fallbackModelId: null
      }))
      
      await this.saveDefaultModelUsages(defaultUsages)
      console.log('已重置为默认配置')
    } catch (error) {
      console.error('重置默认配置失败:', error)
      throw error
    }
  }

  /**
   * 获取使用场景统计信息
   * @returns Promise<{total: number, configured: number, enabled: number}>
   */
  async getUsageStats(): Promise<{total: number, configured: number, enabled: number}> {
    try {
      const usages = await this.getDefaultModelUsages()
      const total = usages.length
      const configured = usages.filter(u => u.selectedModelId).length
      const enabled = usages.filter(u => u.enabled).length

      return { total, configured, enabled }
    } catch (error) {
      console.error('获取使用场景统计信息失败:', error)
      return { total: 0, configured: 0, enabled: 0 }
    }
  }

  /**
   * 导出配置
   * @returns Promise<string>
   */
  async exportConfiguration(): Promise<string> {
    try {
      const usages = await this.getDefaultModelUsages()
      const exportData = {
        version: '1.0',
        exportedAt: new Date().toISOString(),
        usages
      }

      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('导出默认AI模型配置失败:', error)
      throw error
    }
  }

  /**
   * 导入配置
   * @param configJson 配置JSON字符串
   * @returns Promise<void>
   */
  async importConfiguration(configJson: string): Promise<void> {
    try {
      const importData = JSON.parse(configJson)

      if (!importData.usages || !Array.isArray(importData.usages)) {
        throw new Error('无效的配置格式')
      }

      await this.saveDefaultModelUsages(importData.usages)
      console.log('默认AI模型配置导入成功')
    } catch (error) {
      console.error('导入默认AI模型配置失败:', error)
      throw error
    }
  }

  /**
   * 初始化事件监听器
   * 监听AI集成页面的模型选择变化事件
   * @private
   */
  private initializeEventListeners(): void {
    if (DefaultAIModelService.eventListenerInitialized) {
      return
    }

    const handleModelSelectionChange = () => {
      // 延迟执行同步，避免频繁调用
      setTimeout(() => {
        this.syncWithAIIntegration().catch(error => {
          console.warn('响应AI模型选择变化事件失败:', error)
        })
      }, 200)
    }

    // 监听自定义事件
    if (typeof window !== 'undefined') {
      window.addEventListener('aiModelSelectionChanged', handleModelSelectionChange)
    }

    if (typeof document !== 'undefined') {
      document.addEventListener('aiModelSelectionChanged', handleModelSelectionChange)
    }

    DefaultAIModelService.eventListenerInitialized = true
    console.log('AI模型选择变化事件监听器已初始化')
  }

  /**
   * 同步AI集成页面的模型选择变化
   * 当AI集成页面的模型选择发生变化时，更新默认AI模型配置
   * @returns Promise<void>
   */
  async syncWithAIIntegration(): Promise<void> {
    try {
      const selectedModels = await aiIntegrationService.getSelectedModels()
      const availableModels = await this.getAvailableModels()
      const usages = await this.getDefaultModelUsages()
      let hasChanges = false

      // 创建可用模型ID的集合，用于快速查找
      const availableModelIds = new Set(availableModels.map(model => model.id))

      // 检查当前配置的模型是否仍然可用
      for (const usage of usages) {
        let usageChanged = false

        // 检查主要模型
        if (usage.selectedModelId && !availableModelIds.has(usage.selectedModelId)) {
          usage.selectedModelId = null
          usageChanged = true
          console.log(`使用场景 ${usage.name} 的主要模型配置已清除（模型不再可用）`)
        }

        // 检查备用模型
        if (usage.fallbackModelId && !availableModelIds.has(usage.fallbackModelId)) {
          usage.fallbackModelId = null
          usageChanged = true
          console.log(`使用场景 ${usage.name} 的备用模型配置已清除（模型不再可用）`)
        }

        if (usageChanged) {
          hasChanges = true
        }
      }

      // 如果有变化，保存更新后的配置
      if (hasChanges) {
        await this.saveDefaultModelUsages(usages)
        console.log('默认AI模型配置已与AI集成页面同步')
      }
    } catch (error) {
      console.error('同步AI集成页面失败:', error)
    }
  }
}

// 导出单例实例
export const defaultAIModelService = new DefaultAIModelService()