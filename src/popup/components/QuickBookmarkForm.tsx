// 快速收藏表单组件 - 支持即时编辑标题和标签的收藏界面

import React, { useState, useEffect } from 'react'
import { Plus, Tag, X, Sparkles, Loader2 } from 'lucide-react'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Label } from '../../components/ui/label'
import SmartRecognition from './SmartRecognition'

/**
 * 快速收藏表单数据接口
 */
interface QuickBookmarkData {
  title: string
  url: string
  tags: string[]
  favIconUrl?: string
}

/**
 * 快速收藏表单属性接口
 */
interface QuickBookmarkFormProps {
  /** 初始数据 */
  initialData: {
    title: string
    url: string
    favIconUrl?: string
  }
  /** 保存回调 */
  onSave: (data: QuickBookmarkData) => Promise<void>
  /** 取消回调 */
  onCancel: () => void
  /** 是否加载中 */
  loading?: boolean
  /** 智能识别开关状态 */
  smartRecognitionEnabled?: boolean
}

/**
 * 快速收藏表单组件
 * 提供即时编辑标题和标签的收藏界面
 */
const QuickBookmarkForm: React.FC<QuickBookmarkFormProps> = ({
  initialData,
  onSave,
  onCancel,
  loading = false,
  smartRecognitionEnabled = false
}) => {
  const [title, setTitle] = useState(initialData.title || '')
  const [tags, setTags] = useState<string[]>([])
  const [tagInput, setTagInput] = useState('')
  const [showSmartRecognition, setShowSmartRecognition] = useState(false)

  // 初始化标题
  useEffect(() => {
    setTitle(initialData.title || '')
  }, [initialData.title])

  /**
   * 添加标签
   */
  const addTag = (tagName: string) => {
    const trimmedTag = tagName.trim()
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags([...tags, trimmedTag])
    }
    setTagInput('')
  }

  /**
   * 移除标签
   */
  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  /**
   * 处理标签输入键盘事件
   */
  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      if (tagInput.trim()) {
        addTag(tagInput)
      }
    } else if (e.key === 'Backspace' && !tagInput && tags.length > 0) {
      // 如果输入框为空且按下退格键，删除最后一个标签
      removeTag(tags[tags.length - 1])
    }
  }

  /**
   * 处理智能识别完成
   */
  const handleSmartRecognitionComplete = (result: any) => {
    // 应用识别的标签
    if (result.tags && result.tags.length > 0) {
      const newTags = [...new Set([...tags, ...result.tags])] // 去重
      setTags(newTags)
    }
  }

  /**
   * 切换智能识别显示
   */
  const toggleSmartRecognition = () => {
    setShowSmartRecognition(!showSmartRecognition)
  }

  /**
   * 提交表单
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // 如果还有未添加的标签输入，先添加它
    if (tagInput.trim()) {
      addTag(tagInput)
    }

    const bookmarkData: QuickBookmarkData = {
      title: title.trim() || initialData.title,
      url: initialData.url,
      tags: tags,
      favIconUrl: initialData.favIconUrl
    }

    await onSave(bookmarkData)
  }

  return (
    <div className="w-full bg-background container-constrained">
      {/* 头部 */}
      <div className="bg-gradient-to-r from-primary to-primary/90 text-primary-foreground p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Plus className="w-5 h-5" />
            <h1 className="text-lg font-semibold">快速收藏</h1>
          </div>
          <div className="flex space-x-2">
            {/* 智能识别按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSmartRecognition}
              disabled={loading || !title}
              className="text-primary-foreground hover:bg-background/20"
            >
              <Sparkles className="w-3 h-3 mr-1" />
              {showSmartRecognition ? '隐藏识别' : '智能识别'}
            </Button>
          </div>
        </div>
      </div>

      {/* 智能识别区域 */}
      {showSmartRecognition && (
        <div className="bg-muted/30 p-4 border-b">
          <SmartRecognition
            request={{
              title: title,
              url: initialData.url
            }}
            autoTrigger={smartRecognitionEnabled}
            onRecognitionComplete={handleSmartRecognitionComplete}
            disabled={loading}
          />
        </div>
      )}

      {/* 表单内容 */}
      <form onSubmit={handleSubmit} className="p-4 space-y-4">
        {/* 标题输入 */}
        <div className="space-y-2">
          <Label htmlFor="title">标题</Label>
          <Input
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="编辑收藏标题..."
            disabled={loading}
            className="w-full"
          />
        </div>

        {/* 标签输入 */}
        <div className="space-y-2">
          <Label htmlFor="tags">标签</Label>
          <div className="space-y-2">
            {/* 已添加的标签 */}
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {tags.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="text-xs flex items-center space-x-1"
                  >
                    <span>{tag}</span>
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      disabled={loading}
                      className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
            
            {/* 标签输入框 */}
            <div className="flex items-center space-x-2">
              <Tag className="w-4 h-4 text-muted-foreground" />
              <Input
                id="tags"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={handleTagInputKeyDown}
                placeholder="输入标签，按回车添加..."
                disabled={loading}
                className="flex-1"
              />
              {tagInput.trim() && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addTag(tagInput)}
                  disabled={loading}
                >
                  添加
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-4">
          <Button
            type="submit"
            disabled={loading || !title.trim()}
            className="flex-1"
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                收藏中...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                确认收藏
              </>
            )}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            取消
          </Button>
        </div>
      </form>
    </div>
  )
}

export default QuickBookmarkForm
