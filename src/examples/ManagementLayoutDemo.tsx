// 管理页面布局演示 - 展示优化后的分类管理和标签管理页面布局

import React, { useState } from 'react'
import { FolderTree, Tags, RefreshCw } from 'lucide-react'
import ManagementPageLayout from '../components/ManagementPageLayout'
import CategoryCard from '../components/CategoryCard'
import TagCard from '../components/TagCard'
import { Button } from '../components/ui/button'
import type { Category, Tag } from '../types'

// 模拟数据
const mockCategories: (Category & { bookmarkCount: number })[] = [
  {
    id: '1',
    name: '技术开发',
    description: '编程、开发工具、技术文档等相关书签',
    color: '#3B82F6',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-02'),
    bookmarkCount: 25
  },
  {
    id: '2',
    name: '设计资源',
    description: 'UI/UX设计、图标、字体、配色方案等设计相关资源',
    color: '#10B981',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    bookmarkCount: 15
  },
  {
    id: '3',
    name: '这是一个非常非常长的分类名称用来测试标题截断功能',
    description: '这是一个非常非常长的描述文本，用来测试描述文字的截断显示效果，确保在有限的空间内能够正确显示并提供悬停提示功能',
    color: '#F59E0B',
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
    bookmarkCount: 8
  }
]

const mockTags: (Tag & { usageCount: number })[] = [
  {
    id: '1',
    name: 'React',
    color: '#61DAFB',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    usageCount: 45
  },
  {
    id: '2',
    name: 'TypeScript',
    color: '#3178C6',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    usageCount: 32
  },
  {
    id: '3',
    name: '这是一个非常非常长的标签名称用来测试标题截断',
    color: '#8B5CF6',
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
    usageCount: 12
  }
]

const ManagementLayoutDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'categories' | 'tags'>('categories')
  const [loading, setLoading] = useState(false)

  const handleRefresh = () => {
    setLoading(true)
    setTimeout(() => setLoading(false), 1000)
  }

  const handleCreate = () => {
    alert(`创建新的${activeTab === 'categories' ? '分类' : '标签'}`)
  }

  const handleEdit = (id: string) => {
    alert(`编辑 ${activeTab === 'categories' ? '分类' : '标签'}: ${id}`)
  }

  const handleDelete = (id: string) => {
    alert(`删除 ${activeTab === 'categories' ? '分类' : '标签'}: ${id}`)
  }

  const renderCategoryDemo = () => (
    <ManagementPageLayout
      title="分类管理"
      description="管理您的书签分类，更好地组织收藏内容"
      icon={FolderTree}
      loading={loading}
      onRefresh={handleRefresh}
      onCreate={handleCreate}
      createButtonText="新建分类"
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockCategories.map((category) => (
          <CategoryCard
            key={category.id}
            category={category}
            bookmarkCount={category.bookmarkCount}
            onEdit={() => handleEdit(category.id)}
            onDelete={() => handleDelete(category.id)}
          />
        ))}
      </div>
    </ManagementPageLayout>
  )

  const renderTagDemo = () => {
    const extraActions = (
      <Button
        onClick={() => alert('手动同步标签')}
        variant="outline"
        disabled={loading}
        title="从书签中同步标签数据"
      >
        <RefreshCw className="w-4 h-4 mr-2" />
        手动同步
      </Button>
    )

    return (
      <ManagementPageLayout
        title="标签管理"
        description="管理您的书签标签，更好地分类和查找内容"
        icon={Tags}
        loading={loading}
        onRefresh={handleRefresh}
        onCreate={handleCreate}
        createButtonText="新建标签"
        extraActions={extraActions}
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockTags.map((tag) => (
            <TagCard
              key={tag.id}
              tag={tag}
              onEdit={() => handleEdit(tag.id)}
              onDelete={() => handleDelete(tag.id)}
            />
          ))}
        </div>
      </ManagementPageLayout>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 标签切换 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveTab('categories')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'categories'
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            分类管理演示
          </button>
          <button
            onClick={() => setActiveTab('tags')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'tags'
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            标签管理演示
          </button>
        </div>
      </div>

      {/* 内容区域 */}
      {activeTab === 'categories' ? renderCategoryDemo() : renderTagDemo()}
    </div>
  )
}

export default ManagementLayoutDemo
