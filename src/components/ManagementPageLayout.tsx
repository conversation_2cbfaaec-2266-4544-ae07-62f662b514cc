// 管理页面布局组件 - 为分类管理和标签管理提供统一的布局结构

import React from 'react'
import { RefreshCw, Plus, LucideIcon } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'

interface ManagementPageLayoutProps {
  /** 页面标题 */
  title: string
  /** 页面描述 */
  description: string
  /** 页面图标 */
  icon: LucideIcon
  /** 自定义CSS类名 */
  className?: string
  /** 是否正在加载 */
  loading?: boolean
  /** 刷新按钮点击回调 */
  onRefresh?: () => void
  /** 新建按钮点击回调 */
  onCreate?: () => void
  /** 新建按钮文本 */
  createButtonText?: string
  /** 子组件内容 */
  children: React.ReactNode
  /** 额外的头部操作按钮 */
  extraActions?: React.ReactNode
  /** 是否显示刷新按钮 */
  showRefreshButton?: boolean
  /** 是否显示新建按钮 */
  showCreateButton?: boolean
}

/**
 * 管理页面布局组件
 * 提供统一的页面布局结构，包括头部区域、操作按钮和内容区域
 * 用于分类管理和标签管理页面的布局复用
 */
const ManagementPageLayout: React.FC<ManagementPageLayoutProps> = React.memo(({
  title,
  description,
  icon: Icon,
  className = '',
  loading = false,
  onRefresh,
  onCreate,
  createButtonText = '新建',
  children,
  extraActions,
  showRefreshButton = true,
  showCreateButton = true
}) => {
  return (
    <div className={`p-6 bg-background text-foreground ${className}`}>
      {/* 头部区域 */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <CardTitle className="flex items-center text-2xl">
                <Icon className="w-6 h-6 mr-3 text-primary-600 flex-shrink-0" />
                <span className="truncate">{title}</span>
              </CardTitle>
              <CardDescription className="mt-1">
                {description}
              </CardDescription>
            </div>
            
            <div className="flex items-center space-x-3 flex-shrink-0">
              {/* 额外的操作按钮 */}
              {extraActions}
              
              {/* 刷新按钮 */}
              {showRefreshButton && onRefresh && (
                <Button
                  onClick={onRefresh}
                  variant="outline"
                  disabled={loading}
                  title="刷新列表"
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  刷新
                </Button>
              )}
              
              {/* 新建按钮 */}
              {showCreateButton && onCreate && (
                <Button
                  onClick={onCreate}
                  disabled={loading}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  {createButtonText}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 主要内容区域 */}
      <div className="space-y-4 border rounded-md p-4">
        {children}
      </div>
    </div>
  )
})

// 设置显示名称便于调试
ManagementPageLayout.displayName = 'ManagementPageLayout'

export default ManagementPageLayout

// 导出类型定义
export type { ManagementPageLayoutProps }
