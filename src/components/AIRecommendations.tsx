// AI推荐组件 - 显示AI推荐的标签和文件夹

import React, { useState, useEffect } from 'react'
import {
  Sparkles,
  Tag,
  Folder,
  Plus,
  Check,
  Loader2,
  RefreshCw,
  AlertCircle,
  FileText
} from 'lucide-react'

// shadcn组件导入
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'

// 导入标签和分类管理组件
import TagModal from './TagModal'
import CategoryModal from './CategoryModal'
import SmartTagInput from './SmartTagInput'
import SmartFolderSelector from './SmartFolderSelector'
import { tagService } from '../services/tagService'
import { categoryService } from '../services/categoryService'
import type { Tag as TagType, Category, TagInput, CategoryInput } from '../types'

/**
 * AI推荐请求接口
 */
interface AIRecommendationRequest {
  title?: string
  url?: string
  content?: string
  description?: string
  maxRecommendations?: number
}

/**
 * 标签推荐响应接口
 */
interface TagRecommendationResponse {
  existingTags: string[]
  newTags: string[]
  confidence: number
  reasoning?: string
}

/**
 * 文件夹推荐响应接口
 */
interface FolderRecommendationResponse {
  recommendedFolders: Array<{
    name: string
    confidence: number
    reason?: string
  }>
  reasoning?: string
}

/**
 * 描述生成响应接口
 */
interface DescriptionGenerationResponse {
  description: string
  confidence: number
  wordCount: number
  reasoning?: string
}

/**
 * AI推荐组件属性接口
 */
interface AIRecommendationsProps {
  /** 推荐请求数据 */
  request: AIRecommendationRequest
  /** 当前已选择的标签 */
  selectedTags?: string[]
  /** 当前选择的文件夹 */
  selectedFolder?: string
  /** 当前描述内容 */
  currentDescription?: string
  /** 标签选择回调 */
  onTagSelect?: (tag: string) => void
  /** 标签取消选择回调 */
  onTagDeselect?: (tag: string) => void
  /** 文件夹选择回调 */
  onFolderSelect?: (folder: string) => void
  /** 描述选择回调 */
  onDescriptionSelect?: (description: string) => void
  /** 一键接受所有推荐回调 */
  onAcceptAll?: (data: { tags: string[], folder?: string, description?: string }) => void
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示标签推荐 */
  showTagRecommendations?: boolean
  /** 是否显示文件夹推荐 */
  showFolderRecommendations?: boolean
  /** 是否显示描述生成 */
  showDescriptionGeneration?: boolean
  /** 自定义样式类名 */
  className?: string
}

/**
 * AI推荐组件
 * 提供基于AI的标签和文件夹推荐功能
 */
const AIRecommendations: React.FC<AIRecommendationsProps> = ({
  request,
  selectedTags = [],
  selectedFolder,
  currentDescription,
  onTagSelect,
  onTagDeselect,
  onFolderSelect,
  onDescriptionSelect,
  onAcceptAll,
  disabled = false,
  showTagRecommendations = true,
  showFolderRecommendations = true,
  showDescriptionGeneration = true,
  className = ''
}) => {
  const [loading, setLoading] = useState(false)
  const [tagRecommendations, setTagRecommendations] = useState<TagRecommendationResponse | null>(null)
  const [folderRecommendations, setFolderRecommendations] = useState<FolderRecommendationResponse | null>(null)
  const [descriptionGeneration, setDescriptionGeneration] = useState<DescriptionGenerationResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [descriptionLoading, setDescriptionLoading] = useState(false)
  const [hasAcceptedAll, setHasAcceptedAll] = useState(false) // 跟踪是否已执行一键接受

  // 用户实际选择的状态 - 用于一键接受功能
  const [userSelectedTags, setUserSelectedTags] = useState<string[]>([])
  const [userSelectedFolder, setUserSelectedFolder] = useState<string | undefined>(undefined)
  const [userEditedDescription, setUserEditedDescription] = useState<string>('')

  // 新建标签和文件夹的状态
  const [showTagModal, setShowTagModal] = useState(false)
  const [showCategoryModal, setShowCategoryModal] = useState(false)
  const [tagModalLoading, setTagModalLoading] = useState(false)
  const [categoryModalLoading, setCategoryModalLoading] = useState(false)

  // 错误和成功状态管理
  const [tagError, setTagError] = useState<string>('')
  const [tagSuccess, setTagSuccess] = useState<string>('')
  const [folderError, setFolderError] = useState<string>('')
  const [folderSuccess, setFolderSuccess] = useState<string>('')

  /**
   * 获取AI推荐
   */
  const fetchRecommendations = async () => {
    if (!request.title && !request.content && !request.description) {
      return
    }

    // 如果已经执行了一键接受，则不再自动获取推荐
    if (hasAcceptedAll) {
      return
    }

    try {
      setLoading(true)
      setError(null)

      console.log('获取AI推荐:', request)

      // 发送消息到background script - 使用新的全面推荐
      const response = await chrome.runtime.sendMessage({
        type: 'AI_RECOMMEND_ALL',
        data: request
      })

      if (response?.success) {
        setTagRecommendations(response.data.tags)
        setFolderRecommendations(response.data.folders)
        setDescriptionGeneration(response.data.description)
        console.log('AI推荐获取成功:', response.data)

        // 自动选择高置信度的标签（仅当当前没有选择标签时）
        if (response.data.tags && selectedTags.length === 0) {
          const { existingTags, newTags, confidence } = response.data.tags

          // 如果置信度>=0.7，自动选择前几个推荐标签
          if (confidence >= 0.7) {
            const autoSelectTags = []

            // 优先选择现有标签（最多2个）
            if (existingTags && existingTags.length > 0) {
              autoSelectTags.push(...existingTags.slice(0, 2))
            }

            // 如果现有标签不足2个，从新标签中补充（最多1个）
            if (autoSelectTags.length < 2 && newTags && newTags.length > 0) {
              const remainingSlots = 2 - autoSelectTags.length
              autoSelectTags.push(...newTags.slice(0, Math.min(remainingSlots, 1)))
            }

            // 自动选择标签
            if (autoSelectTags.length > 0) {
              console.log('🏷️ 自动选择高置信度标签:', {
                tags: autoSelectTags,
                confidence,
                currentSelected: selectedTags
              })

              // 逐个选择标签
              autoSelectTags.forEach(tag => {
                onTagSelect?.(tag)
              })
            }
          }
        }
      } else {
        throw new Error(response?.error || 'AI推荐失败')
      }
    } catch (error) {
      console.error('获取AI推荐失败:', error)
      setError(error instanceof Error ? error.message : '获取推荐失败')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 刷新推荐
   */
  const handleRefresh = () => {
    fetchRecommendations()
  }

  /**
   * 生成描述
   */
  const generateDescription = async () => {
    if (!request.title && !request.content && !request.url) {
      return
    }

    try {
      setDescriptionLoading(true)
      setError(null)

      console.log('生成AI描述:', request)

      // 发送消息到background script
      const response = await chrome.runtime.sendMessage({
        type: 'AI_GENERATE_DESCRIPTION',
        data: {
          title: request.title,
          url: request.url,
          content: request.content || request.description,
          maxLength: 200
        }
      })

      if (response?.success) {
        setDescriptionGeneration(response.data)
        console.log('AI描述生成成功:', response.data)
      } else {
        throw new Error(response?.error || 'AI描述生成失败')
      }
    } catch (error) {
      console.error('生成AI描述失败:', error)
      setError(error instanceof Error ? error.message : '生成描述失败')
    } finally {
      setDescriptionLoading(false)
    }
  }

  /**
   * 应用生成的描述 - 使用编辑后的内容
   */
  const handleApplyDescription = () => {
    if (userEditedDescription && onDescriptionSelect) {
      onDescriptionSelect(userEditedDescription)
      setDescriptionGeneration(null) // 应用后清除
    }
  }

  /**
   * 处理标签点击
   */
  const handleTagClick = (tag: string) => {
    if (disabled) return

    // 清除之前的错误和成功消息
    setTagError('')
    setTagSuccess('')

    if (selectedTags.includes(tag)) {
      onTagDeselect?.(tag)
      // 从用户选择中移除
      setUserSelectedTags(prev => prev.filter(t => t !== tag))
    } else {
      onTagSelect?.(tag)
      // 添加到用户选择中
      setUserSelectedTags(prev => [...prev, tag])
    }
  }

  /**
   * 处理文件夹点击
   */
  const handleFolderClick = (folder: string) => {
    console.log('🎯 AIRecommendations handleFolderClick:', { folder, disabled, onFolderSelectExists: !!onFolderSelect })
    console.log('🎯 AIRecommendations onFolderSelect函数:', onFolderSelect)

    if (disabled) return

    // 清除之前的错误和成功消息
    setFolderError('')
    setFolderSuccess('')

    console.log('🎯 AIRecommendations调用onFolderSelect:', folder)
    onFolderSelect?.(folder)
    // 更新用户选择的文件夹
    setUserSelectedFolder(folder)
    console.log('🎯 AIRecommendations更新userSelectedFolder:', folder)
  }

  /**
   * 获取置信度颜色
   */
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-gray-600'
  }

  /**
   * 获取置信度文本
   */
  const getConfidenceText = (confidence: number): string => {
    if (confidence >= 0.8) return '高'
    if (confidence >= 0.6) return '中'
    return '低'
  }

  /**
   * 一键接受所有推荐 - 使用用户实际选择的内容
   */
  const handleAcceptAll = () => {
    if (!onAcceptAll) return

    const acceptData: { tags: string[], folder?: string, description?: string } = {
      tags: []
    }

    // 使用用户实际选择的标签（而不是原始推荐）
    acceptData.tags = [...userSelectedTags]

    // 使用用户实际选择的文件夹
    if (userSelectedFolder) {
      acceptData.folder = userSelectedFolder
    }

    // 使用用户编辑后的描述（如果有的话）
    if (userEditedDescription) {
      acceptData.description = userEditedDescription
    }

    // 调用回调函数
    onAcceptAll(acceptData)

    // 设置已接受状态，阻止后续的自动推荐
    setHasAcceptedAll(true)
  }

  /**
   * 检查是否有推荐内容可以接受 - 基于用户实际选择
   */
  const hasRecommendations = () => {
    const hasTags = userSelectedTags.length > 0
    const hasFolders = userSelectedFolder !== undefined
    const hasDescription = userEditedDescription.trim() !== ''

    return hasTags || hasFolders || hasDescription
  }

  // 当请求数据变化时自动获取推荐
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchRecommendations()
    }, 500) // 防抖处理

    return () => clearTimeout(timer)
  }, [request.title, request.content, request.description, request.url])

  // 同步外部传入的选择状态到内部状态
  useEffect(() => {
    setUserSelectedTags([...selectedTags])
  }, [selectedTags])

  useEffect(() => {
    setUserSelectedFolder(selectedFolder)
  }, [selectedFolder])

  useEffect(() => {
    setUserEditedDescription(currentDescription || '')
  }, [currentDescription])

  // 当生成新描述时，自动更新编辑状态
  useEffect(() => {
    if (descriptionGeneration && descriptionGeneration.description) {
      setUserEditedDescription(descriptionGeneration.description)
    }
  }, [descriptionGeneration])

  /**
   * 处理新建标签
   */
  const handleCreateTag = async (tagData: TagInput) => {
    try {
      setTagModalLoading(true)
      setTagError('')
      setTagSuccess('')

      const newTag = await tagService.createTag(tagData)

      // 自动选中新创建的标签
      onTagSelect?.(newTag.name)
      setUserSelectedTags(prev => [...prev, newTag.name])

      // 关闭模态框
      setShowTagModal(false)
      setTagSuccess(`标签 "${newTag.name}" 创建成功`)

      // 3秒后清除成功消息
      setTimeout(() => setTagSuccess(''), 3000)

      console.log('新标签创建成功:', newTag.name)
    } catch (error) {
      console.error('创建标签失败:', error)
      const errorMessage = error instanceof Error ? error.message : '创建标签失败'
      setTagError(errorMessage)
    } finally {
      setTagModalLoading(false)
    }
  }

  /**
   * 处理智能标签输入的新建标签
   */
  const handleSmartTagCreate = async (tagName: string) => {
    try {
      setTagError('')
      setTagSuccess('')

      // 检查标签是否已存在
      const existingTag = await tagService.getTagByName(tagName)
      if (existingTag) {
        setTagError(`标签 "${tagName}" 已存在，请直接选择`)
        return
      }

      // 创建新标签
      const newTag = await tagService.createTag({ name: tagName })

      // 自动选中新创建的标签
      onTagSelect?.(newTag.name)
      setUserSelectedTags(prev => [...prev, newTag.name])

      setTagSuccess(`标签 "${newTag.name}" 创建成功`)

      // 3秒后清除成功消息
      setTimeout(() => setTagSuccess(''), 3000)

      console.log('智能输入新标签创建成功:', newTag.name)
    } catch (error) {
      console.error('智能输入创建标签失败:', error)
      const errorMessage = error instanceof Error ? error.message : '创建标签失败'
      setTagError(errorMessage)
    }
  }

  /**
   * 处理新建文件夹
   */
  const handleCreateCategory = async (categoryData: CategoryInput) => {
    try {
      setCategoryModalLoading(true)
      setFolderError('')
      setFolderSuccess('')

      const newCategory = await categoryService.createCategory(categoryData)

      // 自动选中新创建的文件夹
      onFolderSelect?.(newCategory.name)
      setUserSelectedFolder(newCategory.name)

      // 关闭模态框
      setShowCategoryModal(false)
      setFolderSuccess(`文件夹 "${newCategory.name}" 创建成功`)

      // 3秒后清除成功消息
      setTimeout(() => setFolderSuccess(''), 3000)

      console.log('新文件夹创建成功:', newCategory.name)
    } catch (error) {
      console.error('创建文件夹失败:', error)
      const errorMessage = error instanceof Error ? error.message : '创建文件夹失败'
      setFolderError(errorMessage)
    } finally {
      setCategoryModalLoading(false)
    }
  }

  /**
   * 处理智能文件夹选择的新建文件夹
   */
  const handleSmartFolderCreate = async (folderName: string) => {
    try {
      setFolderError('')
      setFolderSuccess('')

      // 检查文件夹是否已存在
      const categories = await categoryService.getCategories()
      const existingCategory = categories.find(cat =>
        cat.name.toLowerCase() === folderName.toLowerCase()
      )

      if (existingCategory) {
        setFolderError(`文件夹 "${folderName}" 已存在，请直接选择`)
        return
      }

      // 创建新文件夹
      const newCategory = await categoryService.createCategory({ name: folderName })

      // 自动选中新创建的文件夹
      onFolderSelect?.(newCategory.name)
      setUserSelectedFolder(newCategory.name)

      setFolderSuccess(`文件夹 "${newCategory.name}" 创建成功`)

      // 3秒后清除成功消息
      setTimeout(() => setFolderSuccess(''), 3000)

      console.log('智能选择新文件夹创建成功:', newCategory.name)
    } catch (error) {
      console.error('智能选择创建文件夹失败:', error)
      const errorMessage = error instanceof Error ? error.message : '创建文件夹失败'
      setFolderError(errorMessage)
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 标题和操作按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Sparkles className="w-4 h-4 text-primary" />
          <span className="text-sm font-medium text-foreground">AI智能推荐</span>
        </div>
        <div className="flex items-center space-x-2">
          {hasAcceptedAll ? (
            /* 已接受状态提示 */
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-2 text-sm text-green-600">
                <Check className="w-4 h-4" />
                <span>已应用推荐</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setHasAcceptedAll(false)}
                disabled={disabled}
                className="h-8 text-xs text-muted-foreground hover:text-foreground"
                title="重新启用智能推荐"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                重新推荐
              </Button>
            </div>
          ) : (
            <>
              {/* 一键接受按钮 */}
              {hasRecommendations() && onAcceptAll && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleAcceptAll}
                  disabled={loading || disabled}
                  className="h-8 text-xs"
                >
                  <Check className="w-3 h-3 mr-1" />
                  一键接受
                </Button>
              )}
              {/* 刷新按钮 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={loading || disabled}
                className="h-8 w-8 p-0"
              >
                <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-primary" />
          <span className="ml-2 text-sm text-muted-foreground">AI正在分析内容...</span>
        </div>
      )}

      {/* 推荐内容区域 - 只在未接受状态下显示 */}
      {!hasAcceptedAll && (
        <>
          {/* 智能标签输入 */}
          {showTagRecommendations && tagRecommendations && !loading && (
            <SmartTagInput
              recommendedExistingTags={tagRecommendations.existingTags}
              recommendedNewTags={tagRecommendations.newTags}
              selectedTags={selectedTags}
              onTagSelect={handleTagClick}
              onTagDeselect={handleTagClick}
              onCreateTag={handleSmartTagCreate}
              disabled={disabled}
              confidence={tagRecommendations.confidence}
              reasoning={tagRecommendations.reasoning}
              error={tagError}
              success={tagSuccess}
            />
          )}

          {/* 智能文件夹选择 */}
          {showFolderRecommendations && folderRecommendations && !loading && (
            <SmartFolderSelector
              recommendations={folderRecommendations}
              selectedFolder={selectedFolder}
              onFolderSelect={handleFolderClick}
              onCreateFolder={handleSmartFolderCreate}
              disabled={disabled}
              error={folderError}
              success={folderSuccess}
            />
          )}

      {/* 描述生成 */}
      {showDescriptionGeneration && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between text-sm">
              <div className="flex items-center">
                <FileText className="w-4 h-4 mr-2" />
                智能描述生成
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={generateDescription}
                disabled={descriptionLoading || disabled || (!request.title && !request.content && !request.url)}
                className="h-7 text-xs"
              >
                {descriptionLoading ? (
                  <>
                    <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-3 h-3 mr-1" />
                    生成描述
                  </>
                )}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {descriptionGeneration ? (
              <div className="space-y-3">
                {/* 可编辑的描述文本区域 */}
                <div>
                  <Textarea
                    value={userEditedDescription}
                    onChange={(e) => setUserEditedDescription(e.target.value)}
                    placeholder="AI生成的描述将显示在这里，您可以自由编辑..."
                    disabled={disabled}
                    className="min-h-[80px] text-sm leading-relaxed resize-none"
                    rows={4}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <Badge
                      variant="outline"
                      className={`${getConfidenceColor(descriptionGeneration.confidence)}`}
                    >
                      置信度: {getConfidenceText(descriptionGeneration.confidence)}
                    </Badge>
                    <span>字数: {userEditedDescription.length}</span>
                  </div>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleApplyDescription}
                    disabled={disabled || !userEditedDescription.trim()}
                    className="h-7 text-xs"
                  >
                    <Check className="w-3 h-3 mr-1" />
                    应用描述
                  </Button>
                </div>
                {descriptionGeneration.reasoning && (
                  <div className="text-xs text-muted-foreground pt-2 border-t">
                    {descriptionGeneration.reasoning}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                <FileText className="w-6 h-6 mx-auto mb-2 opacity-50" />
                <p className="text-xs">点击"生成描述"按钮，AI将为您生成合适的描述</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

          {/* 无推荐结果 */}
          {!loading && !error &&
           (!tagRecommendations || (tagRecommendations.existingTags.length === 0 && tagRecommendations.newTags.length === 0)) &&
           (!folderRecommendations || folderRecommendations.recommendedFolders.length === 0) &&
           !showDescriptionGeneration && (
            <div className="text-center py-8 text-muted-foreground">
              <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">请输入标题或内容以获取AI推荐</p>
            </div>
          )}
        </>
      )}

      {/* 新建标签模态框 */}
      <TagModal
        isOpen={showTagModal}
        type="create"
        onSave={handleCreateTag}
        onClose={() => setShowTagModal(false)}
        loading={tagModalLoading}
      />

      {/* 新建文件夹模态框 */}
      <CategoryModal
        isOpen={showCategoryModal}
        type="create"
        onSave={handleCreateCategory}
        onClose={() => setShowCategoryModal(false)}
        loading={categoryModalLoading}
      />
    </div>
  )
}

export default AIRecommendations