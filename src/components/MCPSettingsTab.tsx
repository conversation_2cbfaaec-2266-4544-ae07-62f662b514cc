// MCP设置标签页组件

import React, { useState, useCallback } from 'react'
import { DefaultAIModelAPI } from '../services/defaultAIModelAPI'
import { MCPTestService, MCPServerConfig as MCPServerConfigType } from '../services/mcpTestService'
import { 
  Settings, 
  Plus, 
  Trash2, 
  Edit, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  Play,
  Square,
  RefreshCw,
  Eye,
  EyeOff,
  Terminal,
  Globe,
  Database
} from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Alert, AlertDescription } from './ui/alert'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Switch } from './ui/switch'
import { Badge } from './ui/badge'
import { Textarea } from './ui/textarea'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from './ui/dialog'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from './ui/select'

// 使用从服务中导入的MCP服务器配置接口
type MCPServerConfig = MCPServerConfigType

// 预设的MCP服务器模板
const MCP_SERVER_TEMPLATES = [
  {
    id: 'aws-docs',
    name: 'AWS文档',
    command: 'uvx',
    args: ['awslabs.aws-documentation-mcp-server@latest'],
    env: { FASTMCP_LOG_LEVEL: 'ERROR' },
    category: 'api' as const,
    description: 'AWS官方文档查询服务'
  },
  {
    id: 'github',
    name: 'GitHub',
    command: 'uvx',
    args: ['mcp-server-github'],
    env: { GITHUB_PERSONAL_ACCESS_TOKEN: '' },
    category: 'api' as const,
    description: 'GitHub仓库和问题管理'
  },
  {
    id: 'sqlite',
    name: 'SQLite数据库',
    command: 'uvx',
    args: ['mcp-server-sqlite', '--db-path', './data.db'],
    env: {},
    category: 'database' as const,
    description: 'SQLite数据库操作'
  },
  {
    id: 'filesystem',
    name: '文件系统',
    command: 'uvx',
    args: ['mcp-server-filesystem', '--allowed-dirs', './'],
    env: {},
    category: 'file' as const,
    description: '本地文件系统访问'
  },
  {
    id: 'web-search',
    name: '网络搜索',
    command: 'uvx',
    args: ['mcp-server-web-search'],
    env: { SEARCH_API_KEY: '' },
    category: 'api' as const,
    description: '网络搜索功能'
  },
  {
    id: 'fetch',
    name: 'Fetch',
    command: 'uvx',
    args: ['mcp-server-fetch'],
    env: {},
    category: 'api' as const,
    description: '网页内容获取和HTTP请求服务'
  }
]

interface MCPSettingsTabProps {}

const MCPSettingsTab: React.FC<MCPSettingsTabProps> = () => {
  // 状态管理
  const [servers, setServers] = useState<MCPServerConfig[]>([
    {
      id: '1',
      name: 'AWS文档',
      command: 'uvx',
      args: ['awslabs.aws-documentation-mcp-server@latest'],
      env: { FASTMCP_LOG_LEVEL: 'ERROR' },
      disabled: false,
      autoApprove: [],
      status: 'stopped',
      description: 'AWS官方文档查询服务',
      category: 'api'
    },
    {
      id: '2',
      name: 'SQLite数据库',
      command: 'uvx',
      args: ['mcp-server-sqlite', '--db-path', './bookmarks.db'],
      env: {},
      disabled: true,
      autoApprove: ['read_query', 'list_tables'],
      status: 'stopped',
      description: '本地SQLite数据库访问',
      category: 'database'
    },
    {
      id: '3',
      name: 'Fetch',
      command: 'uvx',
      args: ['mcp-server-fetch'],
      env: {},
      disabled: false,
      autoApprove: [],
      status: 'stopped',
      description: '网页内容获取和HTTP请求服务',
      category: 'api'
    }
  ])
  
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [editingServer, setEditingServer] = useState<MCPServerConfig | null>(null)
  const [showEnvVars, setShowEnvVars] = useState<Record<string, boolean>>({})
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [testingServers, setTestingServers] = useState<Set<string>>(new Set())
  const [batchTesting, setBatchTesting] = useState(false)
  
  // 新服务器表单状态
  const [newServer, setNewServer] = useState<Partial<MCPServerConfig>>({
    name: '',
    command: 'uvx',
    args: [],
    env: {},
    disabled: false,
    autoApprove: [],
    category: 'other',
    description: ''
  })

  // 添加新服务器
  const handleAddServer = useCallback(() => {
    if (!newServer.name || !newServer.command) {
      return
    }
    
    const server: MCPServerConfig = {
      id: Date.now().toString(),
      name: newServer.name,
      command: newServer.command,
      args: newServer.args || [],
      env: newServer.env || {},
      disabled: newServer.disabled ?? false,
      autoApprove: newServer.autoApprove || [],
      status: 'stopped',
      description: newServer.description || '',
      category: newServer.category || 'other'
    }
    
    setServers(prev => [...prev, server])
    setShowAddDialog(false)
    resetNewServerForm()
  }, [newServer])

  // 重置新服务器表单
  const resetNewServerForm = () => {
    setNewServer({
      name: '',
      command: 'uvx',
      args: [],
      env: {},
      disabled: false,
      autoApprove: [],
      category: 'other',
      description: ''
    })
    setSelectedTemplate('')
  }

  // 应用模板
  const applyTemplate = useCallback((templateId: string) => {
    const template = MCP_SERVER_TEMPLATES.find(t => t.id === templateId)
    if (template) {
      setNewServer({
        name: template.name,
        command: template.command,
        args: [...template.args],
        env: { ...template.env },
        disabled: false,
        autoApprove: [],
        category: template.category,
        description: template.description
      })
    }
  }, [])

  // 删除服务器
  const handleDeleteServer = useCallback((id: string) => {
    setServers(prev => prev.filter(server => server.id !== id))
  }, [])

  // 切换服务器启用状态
  const handleToggleServer = useCallback((id: string) => {
    setServers(prev => prev.map(server => 
      server.id === id ? { ...server, disabled: !server.disabled } : server
    ))
  }, [])

  // 启动/停止服务器
  const handleToggleServerStatus = useCallback(async (id: string) => {
    const server = servers.find(s => s.id === id)
    if (!server) return
    
    if (server.status === 'running') {
      // 停止服务器
      setServers(prev => prev.map(s => 
        s.id === id ? { ...s, status: 'stopping' } : s
      ))
      
      setTimeout(() => {
        setServers(prev => prev.map(s => 
          s.id === id ? { ...s, status: 'stopped' } : s
        ))
      }, 1500)
    } else {
      // 启动服务器
      setServers(prev => prev.map(s => 
        s.id === id ? { ...s, status: 'starting' } : s
      ))
      
      setTimeout(() => {
        setServers(prev => prev.map(s => 
          s.id === id ? { 
            ...s, 
            status: Math.random() > 0.2 ? 'running' : 'error',
            lastStarted: new Date(),
            errorMessage: Math.random() > 0.2 ? undefined : '连接超时'
          } : s
        ))
      }, 2000)
    }
  }, [servers])

  // 重启服务器
  const handleRestartServer = useCallback(async (id: string) => {
    setServers(prev => prev.map(s => 
      s.id === id ? { ...s, status: 'stopping' } : s
    ))
    
    setTimeout(() => {
      setServers(prev => prev.map(s => 
        s.id === id ? { ...s, status: 'starting' } : s
      ))
      
      setTimeout(() => {
        setServers(prev => prev.map(s => 
          s.id === id ? { 
            ...s, 
            status: 'running',
            lastStarted: new Date(),
            errorMessage: undefined
          } : s
        ))
      }, 1500)
    }, 1000)
  }, [])

  // 切换环境变量显示
  const toggleEnvVarsVisibility = useCallback((id: string) => {
    setShowEnvVars(prev => ({ ...prev, [id]: !prev[id] }))
  }, [])

  // 测试MCP服务器连接
  const handleTestServer = useCallback(async (id: string) => {
    const server = servers.find(s => s.id === id)
    if (!server) return

    // 设置测试状态
    setServers(prev => prev.map(s =>
      s.id === id ? { ...s, testStatus: 'testing' } : s
    ))
    setTestingServers(prev => new Set(prev).add(id))

    try {
      // 使用MCP测试服务进行连接测试
      const testResult = await MCPTestService.testServerConnection(server)

      if (testResult.success) {
        setServers(prev => prev.map(s =>
          s.id === id ? {
            ...s,
            testStatus: 'success',
            lastTestDate: new Date(),
            testResult: `${testResult.message}${testResult.details ? ` - ${testResult.details}` : ''}${testResult.aiModelUsed ? ` (使用模型: ${testResult.aiModelUsed})` : ''}`,
            testError: undefined
          } : s
        ))
      } else {
        throw new Error(testResult.error || testResult.message)
      }
    } catch (error) {
      setServers(prev => prev.map(s =>
        s.id === id ? {
          ...s,
          testStatus: 'failed',
          lastTestDate: new Date(),
          testResult: undefined,
          testError: `测试异常: ${error.message}`
        } : s
      ))
    } finally {
      setTestingServers(prev => {
        const newSet = new Set(prev)
        newSet.delete(id)
        return newSet
      })
    }
  }, [servers])

  // 批量测试所有MCP服务器
  const handleBatchTest = useCallback(async () => {
    const enabledServers = servers.filter(s => !s.disabled)
    if (enabledServers.length === 0) {
      return
    }

    setBatchTesting(true)

    try {
      // 设置所有启用的服务器为测试状态
      setServers(prev => prev.map(s =>
        !s.disabled ? { ...s, testStatus: 'testing' } : s
      ))

      // 使用MCP测试服务进行批量测试
      const testResults = await MCPTestService.testMultipleServers(enabledServers)

      // 更新测试结果
      testResults.forEach((result, index) => {
        const server = enabledServers[index]
        setServers(prev => prev.map(s =>
          s.id === server.id ? {
            ...s,
            testStatus: result.success ? 'success' : 'failed',
            lastTestDate: new Date(),
            testResult: result.success ? `${result.message}${result.details ? ` - ${result.details}` : ''}` : undefined,
            testError: result.success ? undefined : result.error || result.message
          } : s
        ))
      })

    } catch (error) {
      console.error('批量测试失败:', error)
      // 将所有测试中的服务器设置为失败状态
      setServers(prev => prev.map(s =>
        s.testStatus === 'testing' ? {
          ...s,
          testStatus: 'failed',
          lastTestDate: new Date(),
          testError: `批量测试异常: ${error.message}`
        } : s
      ))
    } finally {
      setBatchTesting(false)
    }
  }, [servers])

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-500'
      case 'error': return 'bg-red-500'
      case 'starting': case 'stopping': return 'bg-yellow-500'
      default: return 'bg-gray-500'
    }
  }

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'running': return '运行中'
      case 'error': return '错误'
      case 'starting': return '启动中'
      case 'stopping': return '停止中'
      default: return '已停止'
    }
  }

  // 获取测试状态颜色
  const getTestStatusColor = (testStatus?: string) => {
    switch (testStatus) {
      case 'testing': return 'bg-blue-500'
      case 'success': return 'bg-green-500'
      case 'failed': return 'bg-red-500'
      default: return 'bg-gray-400'
    }
  }

  // 获取测试状态文本
  const getTestStatusText = (testStatus?: string) => {
    switch (testStatus) {
      case 'testing': return '测试中'
      case 'success': return '测试成功'
      case 'failed': return '测试失败'
      default: return '未测试'
    }
  }

  // 获取分类图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'database': return <Database className="w-4 h-4" />
      case 'api': return <Globe className="w-4 h-4" />
      case 'file': return <Terminal className="w-4 h-4" />
      case 'ai': return <Settings className="w-4 h-4" />
      default: return <Settings className="w-4 h-4" />
    }
  }

  // 获取分类名称
  const getCategoryName = (category: string) => {
    switch (category) {
      case 'database': return '数据库'
      case 'api': return 'API服务'
      case 'file': return '文件系统'
      case 'ai': return 'AI服务'
      default: return '其他'
    }
  }

  return (
    <div className="p-6 space-y-8 bg-background text-foreground">
      {/* 页面标题 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="w-6 h-6 mr-3 text-primary" />
            MCP设置
          </CardTitle>
          <CardDescription>
            管理Model Context Protocol (MCP) 服务器，扩展AI助手的功能
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 服务器统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-blue-500" />
              <span className="text-sm font-medium">总服务器</span>
            </div>
            <p className="text-2xl font-bold mt-2">{servers.length}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-green-500" />
              <span className="text-sm font-medium">运行中</span>
            </div>
            <p className="text-2xl font-bold mt-2">
              {servers.filter(s => s.status === 'running').length}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-gray-500" />
              <span className="text-sm font-medium">已停止</span>
            </div>
            <p className="text-2xl font-bold mt-2">
              {servers.filter(s => s.status === 'stopped').length}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-red-500" />
              <span className="text-sm font-medium">错误</span>
            </div>
            <p className="text-2xl font-bold mt-2">
              {servers.filter(s => s.status === 'error').length}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 测试状态统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium">测试成功</span>
            </div>
            <p className="text-2xl font-bold mt-2">
              {servers.filter(s => s.testStatus === 'success').length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4 text-red-500" />
              <span className="text-sm font-medium">测试失败</span>
            </div>
            <p className="text-2xl font-bold mt-2">
              {servers.filter(s => s.testStatus === 'failed').length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Loader2 className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium">测试中</span>
            </div>
            <p className="text-2xl font-bold mt-2">
              {servers.filter(s => s.testStatus === 'testing').length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Settings className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium">未测试</span>
            </div>
            <p className="text-2xl font-bold mt-2">
              {servers.filter(s => !s.testStatus || s.testStatus === 'idle').length}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 添加服务器按钮 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">MCP服务器</h2>
          <p className="text-sm text-muted-foreground">
            配置和管理您的MCP服务器实例
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleBatchTest}
            disabled={batchTesting || servers.filter(s => !s.disabled).length === 0}
          >
            {batchTesting ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="w-4 h-4 mr-2" />
            )}
            批量测试
          </Button>
          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="w-4 h-4 mr-2" />
            添加服务器
          </Button>
        </div>
      </div>

      {/* 服务器列表 */}
      <div className="grid gap-4">
        {servers.map((server) => (
          <Card key={server.id} className={`transition-all ${server.disabled ? 'opacity-60' : ''}`}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  {/* 分类图标 */}
                  <div className="mt-1">
                    {getCategoryIcon(server.category)}
                  </div>
                  
                  {/* 服务器信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="font-semibold text-lg">{server.name}</h3>
                      <Badge variant="outline">{getCategoryName(server.category)}</Badge>
                      
                      {/* 状态指示器 */}
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${getStatusColor(server.status)}`} />
                        <span className="text-sm text-muted-foreground">
                          {getStatusText(server.status)}
                        </span>
                      </div>

                      {/* 测试状态指示器 */}
                      {server.testStatus && (
                        <div className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${getTestStatusColor(server.testStatus)}`} />
                          <span className="text-sm text-muted-foreground">
                            {getTestStatusText(server.testStatus)}
                          </span>
                        </div>
                      )}

                      {server.status === 'running' && server.lastStarted && (
                        <span className="text-xs text-muted-foreground">
                          启动于 {server.lastStarted.toLocaleTimeString()}
                        </span>
                      )}

                      {server.lastTestDate && (
                        <span className="text-xs text-muted-foreground">
                          最后测试: {server.lastTestDate.toLocaleString()}
                        </span>
                      )}
                    </div>
                    
                    {server.description && (
                      <p className="text-sm text-muted-foreground mb-3">{server.description}</p>
                    )}
                    
                    {server.status === 'error' && server.errorMessage && (
                      <Alert variant="destructive" className="mb-3">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{server.errorMessage}</AlertDescription>
                      </Alert>
                    )}

                    {/* 测试结果显示 */}
                    {server.testStatus === 'success' && server.testResult && (
                      <Alert className="mb-3">
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>{server.testResult}</AlertDescription>
                      </Alert>
                    )}

                    {server.testStatus === 'failed' && server.testError && (
                      <Alert variant="destructive" className="mb-3">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{server.testError}</AlertDescription>
                      </Alert>
                    )}
                    
                    {/* 配置信息 */}
                    <div className="space-y-2 text-sm">
                      <div>
                        <Label className="text-xs text-muted-foreground">命令</Label>
                        <p className="font-mono text-xs bg-muted px-2 py-1 rounded">
                          {server.command} {server.args.join(' ')}
                        </p>
                      </div>
                      
                      {Object.keys(server.env).length > 0 && (
                        <div>
                          <div className="flex items-center space-x-2">
                            <Label className="text-xs text-muted-foreground">环境变量</Label>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleEnvVarsVisibility(server.id)}
                            >
                              {showEnvVars[server.id] ? (
                                <EyeOff className="w-3 h-3" />
                              ) : (
                                <Eye className="w-3 h-3" />
                              )}
                            </Button>
                          </div>
                          <div className="font-mono text-xs bg-muted px-2 py-1 rounded">
                            {Object.entries(server.env).map(([key, value]) => (
                              <div key={key}>
                                {key}={showEnvVars[server.id] ? value : '***'}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {server.autoApprove.length > 0 && (
                        <div>
                          <Label className="text-xs text-muted-foreground">自动批准工具</Label>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {server.autoApprove.map(tool => (
                              <Badge key={tool} variant="secondary" className="text-xs">
                                {tool}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* 操作按钮 */}
                <div className="flex items-center space-x-2 ml-4">
                  <Switch
                    checked={!server.disabled}
                    onCheckedChange={() => handleToggleServer(server.id)}
                  />
                  
                  {!server.disabled && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleToggleServerStatus(server.id)}
                        disabled={server.status === 'starting' || server.status === 'stopping'}
                      >
                        {server.status === 'starting' || server.status === 'stopping' ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : server.status === 'running' ? (
                          <Square className="w-4 h-4" />
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                      </Button>
                      
                      {server.status === 'running' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRestartServer(server.id)}
                        >
                          <RefreshCw className="w-4 h-4" />
                        </Button>
                      )}
                    </>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestServer(server.id)}
                    disabled={server.testStatus === 'testing'}
                    title="测试MCP服务器连接"
                  >
                    {server.testStatus === 'testing' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <CheckCircle className="w-4 h-4" />
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingServer(server)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteServer(server.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        
        {servers.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <Settings className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">暂无MCP服务器</h3>
              <p className="text-muted-foreground mb-4">
                点击"添加服务器"按钮开始配置您的第一个MCP服务器
              </p>
              <Button onClick={() => setShowAddDialog(true)}>
                <Plus className="w-4 h-4 mr-2" />
                添加服务器
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertCircle className="w-4 h-4 mr-2" />
            使用说明
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-2">
            <p><strong>MCP (Model Context Protocol)：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>MCP是一个开放协议，用于连接AI助手与外部数据源和工具</li>
              <li>通过MCP服务器，AI可以访问数据库、API、文件系统等资源</li>
              <li>每个服务器都是独立的进程，可以单独启动和停止</li>
            </ul>
            
            <p className="mt-3"><strong>安装要求：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>需要安装 uv 和 uvx：<code className="bg-muted px-1 rounded">pip install uv</code></li>
              <li>uvx 会自动下载和运行MCP服务器包</li>
              <li>某些服务器可能需要额外的环境变量配置</li>
            </ul>
            
            <p className="mt-3"><strong>安全提示：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>谨慎配置自动批准工具，避免意外的数据修改</li>
              <li>定期检查服务器日志，确保正常运行</li>
              <li>敏感的API密钥建议使用环境变量管理</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 添加服务器对话框 */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>添加MCP服务器</DialogTitle>
            <DialogDescription>
              配置新的MCP服务器以扩展AI助手的功能
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* 模板选择 */}
            <div>
              <Label htmlFor="template">选择模板（可选）</Label>
              <Select
                value={selectedTemplate}
                onValueChange={(value) => {
                  setSelectedTemplate(value)
                  if (value) {
                    applyTemplate(value)
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择预设模板或手动配置" />
                </SelectTrigger>
                <SelectContent>
                  {MCP_SERVER_TEMPLATES.map(template => (
                    <SelectItem key={template.id} value={template.id}>
                      <div className="flex items-center space-x-2">
                        {getCategoryIcon(template.category)}
                        <div>
                          <div className="font-medium">{template.name}</div>
                          <div className="text-xs text-muted-foreground">{template.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* 基本信息 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="server-name">服务器名称</Label>
                <Input
                  id="server-name"
                  value={newServer.name || ''}
                  onChange={(e) => setNewServer(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="例如：AWS文档"
                />
              </div>
              
              <div>
                <Label htmlFor="server-category">分类</Label>
                <Select
                  value={newServer.category}
                  onValueChange={(value) => setNewServer(prev => ({ ...prev, category: value as any }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="database">数据库</SelectItem>
                    <SelectItem value="api">API服务</SelectItem>
                    <SelectItem value="file">文件系统</SelectItem>
                    <SelectItem value="ai">AI服务</SelectItem>
                    <SelectItem value="other">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {/* 命令配置 */}
            <div>
              <Label htmlFor="command">命令</Label>
              <Input
                id="command"
                value={newServer.command || ''}
                onChange={(e) => setNewServer(prev => ({ ...prev, command: e.target.value }))}
                placeholder="uvx"
              />
            </div>
            
            <div>
              <Label htmlFor="args">参数（每行一个）</Label>
              <Textarea
                id="args"
                value={newServer.args?.join('\n') || ''}
                onChange={(e) => setNewServer(prev => ({ 
                  ...prev, 
                  args: e.target.value.split('\n').filter(arg => arg.trim()) 
                }))}
                placeholder="awslabs.aws-documentation-mcp-server@latest"
                rows={3}
              />
            </div>
            
            {/* 环境变量 */}
            <div>
              <Label htmlFor="env-vars">环境变量（格式：KEY=VALUE，每行一个）</Label>
              <Textarea
                id="env-vars"
                value={Object.entries(newServer.env || {}).map(([k, v]) => `${k}=${v}`).join('\n')}
                onChange={(e) => {
                  const env: Record<string, string> = {}
                  e.target.value.split('\n').forEach(line => {
                    const [key, ...valueParts] = line.split('=')
                    if (key && valueParts.length > 0) {
                      env[key.trim()] = valueParts.join('=').trim()
                    }
                  })
                  setNewServer(prev => ({ ...prev, env }))
                }}
                placeholder="FASTMCP_LOG_LEVEL=ERROR"
                rows={3}
              />
            </div>
            
            {/* 自动批准工具 */}
            <div>
              <Label htmlFor="auto-approve">自动批准工具（每行一个）</Label>
              <Textarea
                id="auto-approve"
                value={newServer.autoApprove?.join('\n') || ''}
                onChange={(e) => setNewServer(prev => ({ 
                  ...prev, 
                  autoApprove: e.target.value.split('\n').filter(tool => tool.trim()) 
                }))}
                placeholder="read_query&#10;list_tables"
                rows={2}
              />
            </div>
            
            <div>
              <Label htmlFor="description">描述（可选）</Label>
              <Input
                id="description"
                value={newServer.description || ''}
                onChange={(e) => setNewServer(prev => ({ ...prev, description: e.target.value }))}
                placeholder="服务器的简短描述"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowAddDialog(false)
              resetNewServerForm()
            }}>
              取消
            </Button>
            <Button onClick={handleAddServer} disabled={!newServer.name || !newServer.command}>
              添加服务器
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default MCPSettingsTab