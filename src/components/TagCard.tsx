// 标签卡片组件 - 显示单个标签的信息和操作

import React from 'react'
import { Edit, Trash2, Tag, Hash } from 'lucide-react'
import type { Tag as TagType } from '../types'
import { ColorUtils } from '../utils/colorUtils'

interface TagCardProps {
  /** 标签数据 */
  tag: TagType & { usageCount?: number }
  /** 编辑回调 */
  onEdit: () => void
  /** 删除回调 */
  onDelete: () => void
  /** 点击回调 */
  onClick?: () => void
  /** 自定义CSS类名 */
  className?: string
}

/**
 * 标签卡片组件
 * 显示标签的基本信息、使用次数和操作按钮
 * 支持颜色显示、悬停效果和交互反馈
 */
const TagCard: React.FC<TagCardProps> = React.memo(({
  tag,
  onEdit,
  onDelete,
  onClick,
  className = ''
}) => {
  // 处理卡片点击事件
  const handleCardClick = (e: React.MouseEvent) => {
    // 如果点击的是操作按钮，不触发卡片点击
    if ((e.target as HTMLElement).closest('.tag-card-actions')) {
      return
    }
    onClick?.()
  }

  // 处理编辑按钮点击
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEdit()
  }

  // 处理删除按钮点击
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDelete()
  }

  // 格式化时间显示
  const formatTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    
    if (isNaN(dateObj.getTime())) {
      return '未知时间'
    }
    
    const now = new Date()
    const diffMs = now.getTime() - dateObj.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '昨天'
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return dateObj.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  // 获取标签颜色样式
  const getTagColorStyle = () => {
    if (!tag.color) {
      return {
        backgroundColor: '#F8FAFC', // 默认浅灰色背景
        borderColor: '#E2E8F0'
      }
    }

    // 将颜色转换为更浅的背景色和边框色，增加透明度让背景更柔和
    const color = tag.color
    return {
      backgroundColor: `${color}08`, // 更浅的透明度，让背景更柔和
      borderColor: `${color}30`
    }
  }

  // 获取标签颜色指示器样式
  const getColorIndicatorStyle = () => {
    return {
      backgroundColor: tag.color || '#6B7280'
    }
  }

  // 获取对比色文字颜色
  const getContrastTextColor = () => {
    if (!tag.color) return '#374151'
    
    try {
      return ColorUtils.getContrastColor(tag.color)
    } catch {
      return '#374151'
    }
  }

  // 获取使用频率等级
  const getUsageLevel = () => {
    const usageCount = tag.usageCount || 0
    if (usageCount >= 20) return { level: 'high', label: '高频', color: 'bg-red-100 text-red-700' }
    if (usageCount >= 5) return { level: 'medium', label: '中频', color: 'bg-yellow-100 text-yellow-700' }
    if (usageCount >= 1) return { level: 'low', label: '低频', color: 'bg-green-100 text-green-700' }
    return { level: 'none', label: '未使用', color: 'bg-gray-100 text-gray-600' }
  }

  const usageLevel = getUsageLevel()

  return (
    <div
      className={`
        group relative bg-white border-2 rounded-lg p-4 hover:shadow-lg transition-all duration-200 cursor-pointer
        ${onClick ? 'hover:border-gray-300' : ''}
        ${className}
      `}
      style={getTagColorStyle()}
      onClick={handleCardClick}
    >
      {/* 操作按钮区域 - 移到顶部右上角 */}
      <div className="tag-card-actions absolute top-2 right-2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
        {/* 编辑按钮 */}
        <button
          onClick={handleEditClick}
          className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
          title="编辑标签"
          aria-label="编辑标签"
        >
          <Edit className="w-3.5 h-3.5" />
        </button>

        {/* 删除按钮 */}
        <button
          onClick={handleDeleteClick}
          className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          title="删除标签"
          aria-label="删除标签"
        >
          <Trash2 className="w-3.5 h-3.5" />
        </button>
      </div>

      {/* 图标区域 - 独立一行，居中显示 */}
      <div className="flex justify-center mb-3">
        <div className="relative">
          <div
            className="w-12 h-12 rounded-lg flex items-center justify-center shadow-sm"
            style={{
              backgroundColor: tag.color || '#E2E8F0',
              border: `2px solid ${tag.color ? `${tag.color}40` : '#CBD5E1'}`
            }}
          >
            <Tag
              className="w-6 h-6"
              style={{ color: getContrastTextColor() }}
            />
          </div>
          {/* 使用频率指示器 */}
          <div
            className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white shadow-sm ${
              usageLevel.level === 'high' ? 'bg-red-500' :
              usageLevel.level === 'medium' ? 'bg-yellow-500' :
              usageLevel.level === 'low' ? 'bg-green-500' : 'bg-gray-400'
            }`}
            title={`使用频率: ${usageLevel.label}`}
          />
        </div>
      </div>

      {/* 标题区域 - 固定2行高度 */}
      <div className="mb-3">
        <h3
          className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors text-center"
          title={tag.name}
          style={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            wordBreak: 'break-word',
            overflowWrap: 'break-word',
            hyphens: 'auto',
            minHeight: '3.5rem', // 固定2行高度 (1.75rem * 2)
            lineHeight: '1.75rem'
          }}
        >
          {tag.name}
        </h3>
      </div>

      {/* 标签颜色区域 - 独立一行 */}
      <div className="mb-3 flex justify-center">
        <div className="flex items-center space-x-2 px-3 py-1.5 bg-gray-50 rounded-lg">
          <div
            className="w-4 h-4 rounded-full border border-gray-300"
            style={{ backgroundColor: tag.color || '#6B7280' }}
            title={`标签颜色: ${tag.color || '默认'}`}
          />
          <span className="text-xs text-gray-600 font-mono">
            {tag.color || '默认'}
          </span>
        </div>
      </div>

      {/* 统计信息区域 - 固定高度布局 */}
      <div className="space-y-3 mb-4">
        {/* 使用次数 */}
        <div className="flex items-center justify-between py-1">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Hash className="w-4 h-4" />
            <span>使用次数</span>
          </div>
          <span className="text-lg font-semibold text-gray-900">
            {tag.usageCount || 0}
          </span>
        </div>

        {/* 分隔线 */}
        <div className="border-t border-gray-200" />

        {/* 时间信息区域 - 固定高度 */}
        <div className="space-y-2" style={{ minHeight: '2.5rem' }}>
          {/* 创建时间 */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>创建时间</span>
            <span>{formatTime(tag.createdAt)}</span>
          </div>

          {/* 更新时间 - 始终显示，即使为空也保持占位 */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>更新时间</span>
            <span>
              {tag.updatedAt && new Date(tag.updatedAt).getTime() !== new Date(tag.createdAt).getTime()
                ? formatTime(tag.updatedAt)
                : '暂无更新'
              }
            </span>
          </div>
        </div>
      </div>

      {/* 底部状态指示器 - 固定高度 */}
      <div className="flex items-center justify-center" style={{ minHeight: '2rem' }}>
        {/* 使用频率标签 */}
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${usageLevel.color}`}>
          {usageLevel.label}
        </span>
      </div>

      {/* 悬停时的阴影效果增强 */}
      <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-transparent to-transparent group-hover:from-white/10 group-hover:to-transparent pointer-events-none transition-all duration-200" />
    </div>
  )
})

// 设置显示名称便于调试
TagCard.displayName = 'TagCard'

export default TagCard

// 导出类型定义
export type { TagCardProps }