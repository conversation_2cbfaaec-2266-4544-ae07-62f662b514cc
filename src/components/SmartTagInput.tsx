// 智能标签输入组件 - 支持自动匹配和下拉选择的标签输入框

import React, { useState, useEffect, useRef } from 'react'
import { Tag, Plus, Check, X, ChevronDown } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { tagService } from '../services/tagService'
import type { Tag as TagType } from '../types'

/**
 * 智能标签输入组件属性接口
 */
interface SmartTagInputProps {
  /** 推荐的现有标签 */
  recommendedExistingTags?: string[]
  /** 推荐的新标签 */
  recommendedNewTags?: string[]
  /** 当前选中的标签 */
  selectedTags: string[]
  /** 标签选择回调 */
  onTagSelect: (tag: string) => void
  /** 标签取消选择回调 */
  onTagDeselect: (tag: string) => void
  /** 新建标签回调 */
  onCreateTag?: (tagName: string) => void
  /** 是否禁用 */
  disabled?: boolean
  /** 推荐置信度 */
  confidence?: number
  /** 推荐理由 */
  reasoning?: string
  /** 错误信息 */
  error?: string
  /** 成功信息 */
  success?: string
}

/**
 * 智能标签输入组件
 * 提供可编辑的标签输入框，支持自动匹配现有标签和下拉选择
 */
const SmartTagInput: React.FC<SmartTagInputProps> = ({
  recommendedExistingTags = [],
  recommendedNewTags = [],
  selectedTags,
  onTagSelect,
  onTagDeselect,
  onCreateTag,
  disabled = false,
  confidence,
  reasoning,
  error,
  success
}) => {
  const [inputValue, setInputValue] = useState('')
  const [showDropdown, setShowDropdown] = useState(false)
  const [allTags, setAllTags] = useState<TagType[]>([])
  const [filteredTags, setFilteredTags] = useState<TagType[]>([])
  const [loading, setLoading] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // 获取所有现有标签
  useEffect(() => {
    const fetchTags = async () => {
      try {
        setLoading(true)
        const tags = await tagService.getTags()
        setAllTags(tags)
      } catch (error) {
        console.error('获取标签列表失败:', error)
      } finally {
        setLoading(false)
      }
    }
    fetchTags()
  }, [])

  // 根据输入值过滤标签
  useEffect(() => {
    if (!inputValue.trim()) {
      setFilteredTags([])
      setShowDropdown(false)
      return
    }

    const query = inputValue.toLowerCase().trim()
    const filtered = allTags.filter(tag => 
      tag.name.toLowerCase().includes(query) && 
      !selectedTags.includes(tag.name)
    )
    
    setFilteredTags(filtered)
    setShowDropdown(filtered.length > 0)
  }, [inputValue, allTags, selectedTags])

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  /**
   * 处理输入变化
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value)
  }

  /**
   * 处理键盘事件
   */
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    } else if (e.key === 'Escape') {
      setShowDropdown(false)
      setInputValue('')
    }
  }

  /**
   * 添加标签
   */
  const handleAddTag = () => {
    const tagName = inputValue.trim()
    if (!tagName) return

    // 检查是否已选中
    if (selectedTags.includes(tagName)) {
      setInputValue('')
      return
    }

    // 检查是否是现有标签
    const existingTag = allTags.find(tag => 
      tag.name.toLowerCase() === tagName.toLowerCase()
    )

    if (existingTag) {
      // 选择现有标签
      onTagSelect(existingTag.name)
    } else {
      // 创建新标签
      if (onCreateTag) {
        onCreateTag(tagName)
      } else {
        onTagSelect(tagName)
      }
    }

    setInputValue('')
    setShowDropdown(false)
  }

  /**
   * 从下拉框选择标签
   */
  const handleSelectFromDropdown = (tagName: string) => {
    onTagSelect(tagName)
    setInputValue('')
    setShowDropdown(false)
    inputRef.current?.focus()
  }

  /**
   * 处理推荐标签点击
   */
  const handleRecommendedTagClick = (tag: string) => {
    if (selectedTags.includes(tag)) {
      onTagDeselect(tag)
    } else {
      onTagSelect(tag)
    }
  }

  /**
   * 获取置信度颜色
   */
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 border-green-200'
    if (confidence >= 0.6) return 'text-yellow-600 border-yellow-200'
    return 'text-red-600 border-red-200'
  }

  /**
   * 获取置信度文本
   */
  const getConfidenceText = (confidence: number) => {
    if (confidence >= 0.8) return '高'
    if (confidence >= 0.6) return '中'
    return '低'
  }

  return (
    <Card>
      <CardContent className="p-4 space-y-4">
        {/* 标题和置信度 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Tag className="w-4 h-4 mr-2" />
            <span className="text-sm font-medium">推荐标签</span>
            {confidence !== undefined && (
              <Badge 
                variant="outline" 
                className={`ml-2 text-xs ${getConfidenceColor(confidence)}`}
              >
                置信度: {getConfidenceText(confidence)}
              </Badge>
            )}
          </div>
        </div>

        {/* 错误和成功提示 */}
        {error && (
          <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded p-2">
            {error}
          </div>
        )}
        {success && (
          <div className="text-sm text-green-600 bg-green-50 border border-green-200 rounded p-2">
            {success}
          </div>
        )}

        {/* 已选择标签显示区域 */}
        {selectedTags.length > 0 && (
          <div>
            <div className="text-xs text-muted-foreground mb-2">已选择标签</div>
            <div className="flex flex-wrap gap-2">
              {selectedTags.map((tag, index) => (
                <Badge
                  key={index}
                  variant="default"
                  className="cursor-pointer transition-colors hover:bg-destructive/80"
                >
                  <span className="mr-1">{tag}</span>
                  <button
                    onClick={() => !disabled && onTagDeselect(tag)}
                    disabled={disabled}
                    className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                    aria-label={`删除标签 ${tag}`}
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* 标签输入框 */}
        <div className="relative">
          <div className="flex">
            <Input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onFocus={() => inputValue && setShowDropdown(filteredTags.length > 0)}
              placeholder="输入标签名称..."
              disabled={disabled}
              className="rounded-r-none"
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleAddTag}
              disabled={disabled || !inputValue.trim()}
              className="rounded-l-none border-l-0"
              size="sm"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>

          {/* 下拉选择框 */}
          {showDropdown && (
            <div
              ref={dropdownRef}
              className="absolute top-full left-0 right-0 z-50 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto"
            >
              {filteredTags.map((tag) => (
                <div
                  key={tag.id}
                  className="px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center justify-between"
                  onClick={() => handleSelectFromDropdown(tag.name)}
                >
                  <span className="text-sm">{tag.name}</span>
                  <Badge variant="secondary" className="text-xs">
                    {tag.usageCount || 0}次
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 推荐标签区域 */}
        {(recommendedExistingTags.length > 0 || recommendedNewTags.length > 0) && (
          <>
            <Separator />
            
            {/* 现有标签推荐 */}
            {recommendedExistingTags.filter(tag => !selectedTags.includes(tag)).length > 0 && (
              <div>
                <div className="text-xs text-muted-foreground mb-2">推荐现有标签</div>
                <div className="flex flex-wrap gap-2">
                  {recommendedExistingTags
                    .filter(tag => !selectedTags.includes(tag))
                    .map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className={`cursor-pointer transition-colors ${
                          disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent'
                        }`}
                        onClick={() => !disabled && handleRecommendedTagClick(tag)}
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                </div>
              </div>
            )}

            {/* 新标签建议 */}
            {recommendedNewTags.filter(tag => !selectedTags.includes(tag)).length > 0 && (
              <div>
                {recommendedExistingTags.filter(tag => !selectedTags.includes(tag)).length > 0 && <Separator className="my-3" />}
                <div className="text-xs text-muted-foreground mb-2">新标签建议</div>
                <div className="flex flex-wrap gap-2">
                  {recommendedNewTags
                    .filter(tag => !selectedTags.includes(tag))
                    .map((tag, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className={`cursor-pointer transition-colors ${
                          disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent'
                        }`}
                        onClick={() => !disabled && handleRecommendedTagClick(tag)}
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                </div>
              </div>
            )}

            {/* 推荐理由 */}
            {reasoning && (
              <div className="text-xs text-muted-foreground pt-2 border-t">
                {reasoning}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default SmartTagInput
