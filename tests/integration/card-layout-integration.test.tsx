/**
 * 卡片布局集成测试
 * 验证新的卡片样式在插件环境中正确工作
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import TagCard from '../../src/components/TagCard'
import CategoryCard from '../../src/components/CategoryCard'
import type { Tag, Category } from '../../src/types'

describe('卡片布局集成测试', () => {
  const mockTag: Tag = {
    id: '1',
    name: '前端开发技术',
    color: '#3B82F6',
    usageCount: 25,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  }

  const mockCategory: Category = {
    id: '1',
    name: '技术文档',
    description: '收集各种技术文档和学习资料',
    color: '#8B5CF6',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z'
  }

  describe('TagCard 布局优化验证', () => {
    it('应该正确渲染优化后的标签卡片布局', () => {
      render(
        <TagCard
          tag={mockTag}
          onEdit={() => {}}
          onDelete={() => {}}
        />
      )

      // 验证标签名称正确显示
      expect(screen.getByText('前端开发技术')).toBeInTheDocument()
      
      // 验证使用次数正确显示
      expect(screen.getByText('25')).toBeInTheDocument()
      
      // 验证颜色值正确显示
      expect(screen.getByText('#3B82F6')).toBeInTheDocument()
      
      // 验证更新时间正确显示
      expect(screen.getByText('更新时间')).toBeInTheDocument()
    })

    it('应该正确处理长标题的截断', () => {
      const longNameTag = {
        ...mockTag,
        name: '这是一个非常非常长的标签名称，用来测试标题的固定高度和截断效果，确保布局的一致性'
      }

      render(
        <TagCard
          tag={longNameTag}
          onEdit={() => {}}
          onDelete={() => {}}
        />
      )

      // 验证长标题正确显示
      expect(screen.getByText(longNameTag.name)).toBeInTheDocument()
      
      // 验证标题元素具有正确的样式类
      const titleElement = screen.getByText(longNameTag.name)
      expect(titleElement).toHaveClass('text-center')
    })

    it('应该正确显示空更新时间的占位内容', () => {
      const tagWithoutUpdate = {
        ...mockTag,
        updatedAt: mockTag.createdAt // 相同时间表示没有更新
      }

      render(
        <TagCard
          tag={tagWithoutUpdate}
          onEdit={() => {}}
          onDelete={() => {}}
        />
      )

      // 验证显示占位内容
      expect(screen.getByText('暂无更新')).toBeInTheDocument()
    })
  })

  describe('CategoryCard 布局优化验证', () => {
    it('应该正确渲染优化后的分类卡片布局', () => {
      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={18}
          onEdit={() => {}}
          onDelete={() => {}}
        />
      )

      // 验证分类名称正确显示
      expect(screen.getByText('技术文档')).toBeInTheDocument()
      
      // 验证描述正确显示
      expect(screen.getByText('收集各种技术文档和学习资料')).toBeInTheDocument()
      
      // 验证书签数量正确显示
      expect(screen.getByText('18')).toBeInTheDocument()
      
      // 验证颜色值正确显示
      expect(screen.getByText('#8B5CF6')).toBeInTheDocument()
      
      // 验证状态正确显示
      expect(screen.getByText('活跃')).toBeInTheDocument()
    })

    it('应该正确处理长标题和描述的截断', () => {
      const longContentCategory = {
        ...mockCategory,
        name: '这是一个非常非常长的分类名称，用来测试标题的固定高度和截断效果',
        description: '这是一个非常详细的描述文本，用来测试描述区域的固定高度和截断效果，确保布局的一致性'
      }

      render(
        <CategoryCard
          category={longContentCategory}
          bookmarkCount={5}
          onEdit={() => {}}
          onDelete={() => {}}
        />
      )

      // 验证长标题和描述正确显示
      expect(screen.getByText(longContentCategory.name)).toBeInTheDocument()
      expect(screen.getByText(longContentCategory.description)).toBeInTheDocument()
      
      // 验证元素具有正确的样式类
      const titleElement = screen.getByText(longContentCategory.name)
      const descriptionElement = screen.getByText(longContentCategory.description)
      expect(titleElement).toHaveClass('text-center')
      expect(descriptionElement).toHaveClass('text-center')
    })

    it('应该正确显示空分类状态', () => {
      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={0}
          onEdit={() => {}}
          onDelete={() => {}}
        />
      )

      // 验证空分类状态正确显示
      expect(screen.getByText('空分类')).toBeInTheDocument()
    })

    it('应该正确显示空更新时间的占位内容', () => {
      const categoryWithoutUpdate = {
        ...mockCategory,
        updatedAt: mockCategory.createdAt // 相同时间表示没有更新
      }

      render(
        <CategoryCard
          category={categoryWithoutUpdate}
          bookmarkCount={5}
          onEdit={() => {}}
          onDelete={() => {}}
        />
      )

      // 验证显示占位内容
      expect(screen.getByText('暂无更新')).toBeInTheDocument()
    })
  })

  describe('布局一致性验证', () => {
    it('所有卡片应该具有一致的布局结构', () => {
      const { container: tagContainer } = render(
        <TagCard
          tag={mockTag}
          onEdit={() => {}}
          onDelete={() => {}}
        />
      )

      const { container: categoryContainer } = render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={18}
          onEdit={() => {}}
          onDelete={() => {}}
        />
      )

      // 验证两种卡片都有相似的结构
      const tagCard = tagContainer.querySelector('.group')
      const categoryCard = categoryContainer.querySelector('.group')
      
      expect(tagCard).toBeInTheDocument()
      expect(categoryCard).toBeInTheDocument()
      
      // 验证都有操作按钮区域
      expect(tagContainer.querySelector('[aria-label="编辑标签"]')).toBeInTheDocument()
      expect(tagContainer.querySelector('[aria-label="删除标签"]')).toBeInTheDocument()
      expect(categoryContainer.querySelector('[aria-label="编辑分类"]')).toBeInTheDocument()
      expect(categoryContainer.querySelector('[aria-label="删除分类"]')).toBeInTheDocument()
    })
  })
})
