/**
 * MCP测试服务单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { MCPTestService } from '../src/services/mcpTestService'
import { DefaultAIModelAPI } from '../src/services/defaultAIModelAPI'

// Mock DefaultAIModelAPI
vi.mock('../src/services/defaultAIModelAPI', () => ({
  DefaultAIModelAPI: {
    getDefaultChatModel: vi.fn(),
    callModel: vi.fn()
  }
}))

describe('MCPTestService', () => {
  const mockServer = {
    id: 'test-1',
    name: 'Test Fetch Server',
    command: 'uvx',
    args: ['mcp-server-fetch'],
    env: {},
    disabled: false,
    autoApprove: [],
    status: 'stopped' as const,
    description: '网页内容获取和HTTP请求服务',
    category: 'api' as const
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('testServerConnection', () => {
    it('应该在未配置默认AI模型时返回失败', async () => {
      // Mock: 未配置默认AI模型
      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(null)

      const result = await MCPTestService.testServerConnection(mockServer)

      expect(result.success).toBe(false)
      expect(result.error).toContain('未配置默认AI模型')
    })

    it('应该在配置不完整时返回失败', async () => {
      // Mock: 配置了默认AI模型
      const mockModel = {
        id: 'openai_gpt-3.5-turbo',
        name: 'gpt-3.5-turbo',
        displayName: 'GPT-3.5 Turbo',
        provider: 'OpenAI',
        providerId: 'openai'
      }
      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(mockModel)

      const incompleteServer = {
        ...mockServer,
        command: '',
        args: []
      }

      const result = await MCPTestService.testServerConnection(incompleteServer)

      expect(result.success).toBe(false)
      expect(result.error).toContain('配置不完整')
    })

    it('应该在AI模型调用成功时返回成功', async () => {
      // Mock: 配置了默认AI模型
      const mockModel = {
        id: 'openai_gpt-3.5-turbo',
        name: 'gpt-3.5-turbo',
        displayName: 'GPT-3.5 Turbo',
        provider: 'OpenAI',
        providerId: 'openai'
      }
      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(mockModel)

      // Mock: AI模型调用成功
      const mockResponse = '这是一个用于网页内容获取的MCP服务器，连接状态正常。'
      vi.mocked(DefaultAIModelAPI.callModel).mockResolvedValue(mockResponse)

      const result = await MCPTestService.testServerConnection(mockServer)

      expect(result.success).toBe(true)
      expect(result.message).toBe('MCP服务器连接测试成功')
      expect(result.details).toContain(mockResponse)
      expect(result.aiModelUsed).toBe(mockModel.displayName)
      expect(result.responseTime).toBeGreaterThan(0)
    })

    it('应该在AI模型调用失败时返回失败', async () => {
      // Mock: 配置了默认AI模型
      const mockModel = {
        id: 'openai_gpt-3.5-turbo',
        name: 'gpt-3.5-turbo',
        displayName: 'GPT-3.5 Turbo',
        provider: 'OpenAI',
        providerId: 'openai'
      }
      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(mockModel)

      // Mock: AI模型调用失败
      vi.mocked(DefaultAIModelAPI.callModel).mockResolvedValue(null)

      const result = await MCPTestService.testServerConnection(mockServer)

      expect(result.success).toBe(false)
      expect(result.error).toContain('AI模型调用失败')
    })
  })

  describe('testToolCall', () => {
    it('应该成功测试数据库工具调用', async () => {
      const dbServer = {
        ...mockServer,
        category: 'database' as const
      }

      const result = await MCPTestService.testToolCall(dbServer, 'query', { query: 'SELECT * FROM users' })

      expect(result.success).toBe(true)
      expect(result.toolName).toBe('query')
      expect(result.result).toHaveProperty('query')
      expect(result.result).toHaveProperty('rows')
      expect(result.responseTime).toBeGreaterThan(0)
    })

    it('应该成功测试API工具调用', async () => {
      const result = await MCPTestService.testToolCall(mockServer, 'fetch', { url: 'https://example.com' })

      expect(result.success).toBe(true)
      expect(result.toolName).toBe('fetch')
      expect(result.result).toHaveProperty('status', 'success')
      expect(result.result).toHaveProperty('data')
      expect(result.responseTime).toBeGreaterThan(0)
    })

    it('应该成功测试文件系统工具调用', async () => {
      const fileServer = {
        ...mockServer,
        category: 'file' as const
      }

      const result = await MCPTestService.testToolCall(fileServer, 'read', { path: '/test/file.txt' })

      expect(result.success).toBe(true)
      expect(result.toolName).toBe('read')
      expect(result.result).toHaveProperty('operation', 'read')
      expect(result.result).toHaveProperty('path')
      expect(result.responseTime).toBeGreaterThan(0)
    })
  })

  describe('testMultipleServers', () => {
    it('应该批量测试多个服务器', async () => {
      const servers = [
        mockServer,
        {
          ...mockServer,
          id: 'test-2',
          name: 'Test SQLite Server',
          category: 'database' as const
        }
      ]

      // Mock: 配置了默认AI模型
      const mockModel = {
        id: 'openai_gpt-3.5-turbo',
        name: 'gpt-3.5-turbo',
        displayName: 'GPT-3.5 Turbo',
        provider: 'OpenAI',
        providerId: 'openai'
      }
      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(mockModel)

      // Mock: AI模型调用成功
      vi.mocked(DefaultAIModelAPI.callModel).mockResolvedValue('测试成功')

      const results = await MCPTestService.testMultipleServers(servers)

      expect(results).toHaveLength(2)
      expect(results[0].success).toBe(true)
      expect(results[1].success).toBe(true)
    })

    it('应该跳过已禁用的服务器', async () => {
      const servers = [
        mockServer,
        {
          ...mockServer,
          id: 'test-2',
          disabled: true
        }
      ]

      // Mock: 配置了默认AI模型
      const mockModel = {
        id: 'openai_gpt-3.5-turbo',
        name: 'gpt-3.5-turbo',
        displayName: 'GPT-3.5 Turbo',
        provider: 'OpenAI',
        providerId: 'openai'
      }
      vi.mocked(DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(mockModel)

      // Mock: AI模型调用成功
      vi.mocked(DefaultAIModelAPI.callModel).mockResolvedValue('测试成功')

      const results = await MCPTestService.testMultipleServers(servers)

      expect(results).toHaveLength(1)
      expect(results[0].success).toBe(true)
    })
  })
})
