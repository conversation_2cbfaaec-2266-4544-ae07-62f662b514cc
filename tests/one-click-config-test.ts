/**
 * 一键设置推荐配置功能测试
 * 测试"一键设置推荐配置"按钮是否能正确更新详细配置区域的模型选择器
 */

import { defaultAIModelService } from '../src/services/defaultAIModelService'
import { aiIntegrationService } from '../src/services/aiIntegrationService'

/**
 * 一键配置功能测试类
 */
export class OneClickConfigTest {
  /**
   * 运行所有测试
   */
  static async runAllTests(): Promise<void> {
    console.log('🧪 开始一键设置推荐配置功能测试...')
    
    try {
      await this.cleanupTestData()
      await this.setupTestEnvironment()
      await this.testOneClickConfiguration()
      await this.testSyncAfterConfiguration()
      await this.cleanupTestData()
      
      console.log('✅ 所有测试通过！')
    } catch (error) {
      console.error('❌ 测试失败:', error)
      await this.cleanupTestData()
      throw error
    }
  }

  /**
   * 设置测试环境
   */
  static async setupTestEnvironment(): Promise<void> {
    console.log('🔧 设置测试环境...')
    
    // 添加测试提供商
    const testProvider = {
      name: '测试提供商OneClick',
      type: 'openai' as const,
      baseUrl: 'https://api.openai.com/v1',
      apiKey: 'test-key-oneclick',
      enabled: true
    }
    
    const addedProvider = await aiIntegrationService.configureProvider(testProvider)
    
    // 选择一个测试模型
    await aiIntegrationService.saveSelectedModel(addedProvider.id, 'gpt-3.5-turbo')
    
    console.log('✅ 测试环境设置完成')
  }

  /**
   * 测试一键设置推荐配置功能
   */
  static async testOneClickConfiguration(): Promise<void> {
    console.log('🧪 测试一键设置推荐配置功能...')
    
    // 获取可用模型
    const availableModels = await defaultAIModelService.getAvailableModels()
    if (availableModels.length === 0) {
      throw new Error('没有可用的模型进行测试')
    }
    
    const primaryModel = availableModels[0]
    const fallbackModel = availableModels.length > 1 ? availableModels[1] : null
    
    console.log(`使用主要模型: ${primaryModel.displayName}`)
    if (fallbackModel) {
      console.log(`使用备选模型: ${fallbackModel.displayName}`)
    }
    
    // 执行一键设置推荐配置
    await defaultAIModelService.setRecommendedConfigurationWithModels(
      primaryModel.id,
      fallbackModel?.id || null
    )
    
    // 验证配置是否正确应用
    const usages = await defaultAIModelService.getDefaultModelUsages()
    
    for (const usage of usages) {
      if (usage.selectedModelId !== primaryModel.id) {
        throw new Error(`使用场景 ${usage.name} 的主要模型配置不正确，期望: ${primaryModel.id}, 实际: ${usage.selectedModelId}`)
      }
      
      if (fallbackModel && usage.fallbackModelId !== fallbackModel.id) {
        throw new Error(`使用场景 ${usage.name} 的备选模型配置不正确，期望: ${fallbackModel.id}, 实际: ${usage.fallbackModelId}`)
      }
    }
    
    console.log('✅ 一键设置推荐配置功能测试通过')
  }

  /**
   * 测试配置后的同步功能
   */
  static async testSyncAfterConfiguration(): Promise<void> {
    console.log('🧪 测试配置后的同步功能...')
    
    // 获取配置前的状态
    const usagesBefore = await defaultAIModelService.getDefaultModelUsages()
    const configuredUsages = usagesBefore.filter(u => u.selectedModelId)
    
    if (configuredUsages.length === 0) {
      throw new Error('没有已配置的使用场景进行测试')
    }
    
    console.log(`配置前有 ${configuredUsages.length} 个已配置的使用场景`)
    
    // 执行同步（模拟loadData中的syncWithAIIntegration调用）
    await defaultAIModelService.syncWithAIIntegration()
    
    // 验证同步后配置仍然存在
    const usagesAfter = await defaultAIModelService.getDefaultModelUsages()
    const configuredUsagesAfter = usagesAfter.filter(u => u.selectedModelId)
    
    if (configuredUsagesAfter.length !== configuredUsages.length) {
      throw new Error(`同步后配置数量不匹配，期望: ${configuredUsages.length}, 实际: ${configuredUsagesAfter.length}`)
    }
    
    // 验证每个配置的模型ID是否仍然正确
    for (const usage of configuredUsagesAfter) {
      const originalUsage = configuredUsages.find(u => u.id === usage.id)
      if (!originalUsage) continue
      
      if (usage.selectedModelId !== originalUsage.selectedModelId) {
        throw new Error(`使用场景 ${usage.name} 的模型配置在同步后被错误清除`)
      }
    }
    
    console.log('✅ 配置后的同步功能测试通过')
  }

  /**
   * 清理测试数据
   */
  static async cleanupTestData(): Promise<void> {
    console.log('🧹 清理测试数据...')
    
    try {
      // 清理提供商配置
      const providers = await aiIntegrationService.getConfiguredProviders()
      for (const provider of providers) {
        if (provider.name.includes('测试提供商OneClick')) {
          await aiIntegrationService.removeProvider(provider.id)
        }
      }
      
      // 清理默认模型配置
      const defaultUsages = await defaultAIModelService.getDefaultModelUsages()
      const cleanedUsages = defaultUsages.map(usage => ({
        ...usage,
        selectedModelId: null,
        fallbackModelId: null
      }))
      await defaultAIModelService.saveDefaultModelUsages(cleanedUsages)
      
      console.log('✅ 测试数据清理完成')
    } catch (error) {
      console.warn('⚠️ 清理测试数据时出现警告:', error)
    }
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && window.location?.pathname?.includes('test')) {
  OneClickConfigTest.runAllTests().catch(console.error)
}
